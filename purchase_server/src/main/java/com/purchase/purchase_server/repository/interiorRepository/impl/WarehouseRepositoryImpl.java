package com.purchase.purchase_server.repository.interiorRepository.impl;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.SenboWarehouseVo;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import static com.crafts_mirror.utils.constant.SystemConstant.SEN_BO_WAREHOUSE_INFO_GET_URL;

/**
 * @Description 获取森帛仓库信息
 * <AUTHOR>
 * @Date 2024/7/16 17:56
 **/
@Service
public class WarehouseRepositoryImpl implements WarehouseRepository {

    @Resource
    protected RestTemplate restTemplate;

    @Override
    public List<SenboWarehouseDto> getSenboWarehouseList() {
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO warehouseVo = restTemplateUtils.get(SEN_BO_WAREHOUSE_INFO_GET_URL, ResultDTO.class);
        List<SenboWarehouseDto> senboWarehouseList = JSON.to(SenboWarehouseVo.class, warehouseVo.getData()).getSenboWarehouseList();
        return senboWarehouseList.stream().filter(f -> !"8".equals(f.getSenboWarehouseId())).toList();
    }
}
