package com.purchase.purchase_server.utils.easyExcelUtil.template.Deilvery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/8
 **/
public class ExcelConstants {

    // 备货规则
    public static class Sheet1 {
        public static final List<String> FIXED_COLUMNS = List.of(
                "*虚拟SKU",
                "*国内中转天数",
                "*安全天数",
                "*发货周期"
        );
    }

    // 目标日销
    public static class Sheet2 {
        public static final List<String> FIXED_COLUMNS = List.of(
                "*虚拟SKU"
        );
    }

    // 亚马逊库存概况
    public static class Sheet3 {
        public static final List<String> FIXED_COLUMNS = List.of(
                "虚拟SKU",
                "FBA(AM)",
                "FBA(AC)",
                "FBA(AMEU)",
                "FBA(AK)",
                "正在接收中",
                "预留库存（转运+处理中）"
        );
    }

    // BTB库存概况
    public static class Sheet4 {
        public static final List<String> FIXED_COLUMNS = List.of(
                "虚拟SKU"
        );
    }

    // 其他库存概况
    public static class Sheet5 {
        public static final List<String> FIXED_COLUMNS = List.of(
                "虚拟SKU"
        );
    }

    // 灯具库存概况
    public static class Sheet6 {
        public static final List<String> FIXED_COLUMNS = List.of(
                "虚拟SKU",
                "FBA(AM)",
                "FBA(AC)",
                "FBA(AMEU)",
                "FBA(AMEU)-K",
                "预留库存（转运+处理中）"
        );
    }

    // 在途
    public static class Sheet7 {
        public static final List<String> FIXED_COLUMNS = List.of(
                "*货件号",
                "*虚拟SKU",
                "*库存数",
                "*库存可用时间",
                "*仓库",
                "*出货时间",
                "备注"
        );
    }

    // 计划
    public static class Sheet8 {
        public static final List<String> FIXED_COLUMNS = List.of(
                "*合同号",
                "*虚拟SKU",
                "*工厂交期",
                "*数量",
                "备注"
        );
    }
}
