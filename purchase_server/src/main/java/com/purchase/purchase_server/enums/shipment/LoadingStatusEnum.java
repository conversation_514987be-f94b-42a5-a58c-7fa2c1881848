package com.purchase.purchase_server.enums.shipment;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum LoadingStatusEnum {

    UNDER_LOAD(0, "未装满"),
    WHOLE_CONTAINER(1, "整柜"),
    BULK_PARTS(2, "散件"),
    NULL_STATUS(9, ""),// 无装载状态
    ;

    private final Integer code;
    private final String desc;

    public static LoadingStatusEnum getLoadingStatusByCode(int code) {
        return Arrays.stream(LoadingStatusEnum.values())
                .filter(f -> f.getCode() == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未找到合适的装柜状态，请确认"));
    }
}
