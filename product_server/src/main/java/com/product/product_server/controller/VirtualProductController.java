package com.product.product_server.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.ChannelSearchVo;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.product.product_server.entity.LogTrackNumMapDto;
import com.product.product_server.entity.dto.ProductCategoryDTO;
import com.product.product_server.entity.dto.VirtualShippingRatioDto;
import com.product.product_server.entity.dto.VirtualSkuProductSaveDto;
import com.product.product_server.entity.form.*;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.entity.vo.*;
import com.product.product_server.service.IChannelService;
import com.product.product_server.service.IVirtualProductService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static com.product.product_server.utils.easyExcelUtil.listener.AbstractUpdateVirtualProductInfoListener.UpdateType.*;

/**
 * @Description 虚拟商品controller层
 * <AUTHOR>
 * @Date 2023/12/12 16:49
 **/
@Slf4j
@RestController
@RequestMapping("virtualProduct")
public class VirtualProductController {

    @Resource
    private IVirtualProductService virtualProductService;

    @Resource
    private IChannelService channelService;

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("check/oldSku")
    @ResponseBody
    public ResultDTO<ExistedVo> checkOldSkuExisted(@RequestBody ExistedForm form) {
        return ResultDTO.success(ExistedVo.builder()
                .isExisted(virtualProductService.checkOldSkuExisted(form.getSku()))
                .build());
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("check/virtualSku")
    @ResponseBody
    public ResultDTO<ExistedVo> checkVirtualSkuExisted(@RequestBody ExistedForm form) {
        return ResultDTO.success(ExistedVo.builder()
                .isExisted(virtualProductService.checkVirtualSkuExisted(form.getSku()))
                .build());
    }

    @RequiresPermissions("product:virtualProduct:list")
    @GetMapping("/detail")
    @ResponseBody
    public ResultDTO<VirtualProductDetailVo> productDetail(@RequestParam("virtualProductId") String virtualProductId) {
        return ResultDTO.success(virtualProductService.getDetail(virtualProductId));
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("save/virtualProduct")
    @ResponseBody
    public ResultDTO<Boolean> saveVirtualSku(@RequestBody VirtualProductSkuSaveForm form) {
        String username = SecurityUtils.getUsername();
        List<VirtualSkuProductSaveDto> dtoList = form.getVirtualSkuProductSaveDtoList();
        return ResultDTO.success(virtualProductService.saveVirtualProduct(dtoList,
                new LogTrackNumDto(dtoList.getFirst().getSelfProductSkuId()), username));
    }

    @PostMapping("channelSet/all")
    @ResponseBody
    public ResultDTO<ChannelSearchVo> getAllChannelSet(@RequestBody CheckChannelForm form) {
        return ResultDTO.success(channelService.getAllNicheChannel());
    }

    @PostMapping("channelSet/notExisted")
    @ResponseBody
    public ResultDTO<ChannelSearchVo> getNotExistedChannelSet(@RequestBody CheckChannelForm form) {
        return ResultDTO.success(virtualProductService.getNotExistedChannelSet(form.selfSkuList()));
    }

    @PostMapping("channelSet/existed")
    @ResponseBody
    public ResultDTO<ChannelSearchVo> getExistedChannelSet(@RequestBody CheckChannelForm form) {
        return ResultDTO.success(virtualProductService.getExistedChannelSet(form.selfSkuList()));
    }

    /**
     * 虚拟sku列表页面
     *
     * @param form 搜索产品列表页
     * @return 产品列表
     */
    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/pageList")
    @ResponseBody
    public ResultDTO<IPage<VirtualProductListVo>> productListData(@RequestBody VirtualProductSearchForm form) {
        IPage<VirtualProductListVo> selfProductVo = virtualProductService.pageList(form);
        return ResultDTO.success(selfProductVo);
    }

    /**
     * 创建父spu时获取虚拟sku列表页面
     *
     * @param form 搜索产品列表页
     * @return 产品列表
     */
    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/pageList/forSpu")
    @ResponseBody
    public ResultDTO<IPage<VirtualProductForSpuVo>> productListDataForSpu(@RequestBody VirtualProductSearchForSpuForm form) {
        return ResultDTO.success(virtualProductService.pageListForSpu(form));
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/list/underSpu")
    @ResponseBody
    public ResultDTO<VirtualSkuInfoUnderSpuVo> virtualSkuInfoUnderSpu(@RequestBody VirtualSkuInfoUnderSpuForm form) {
        return ResultDTO.success(VirtualSkuInfoUnderSpuVo.builder()
                .virtualProductListVoList(virtualProductService.getVirtualProductListUnderSpu(form))
                .build());
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("update/virtualProduct")
    @ResponseBody
    public ResultDTO<Boolean> saveRemarks(@RequestBody VirtualUpdateForm form) {
        try {
            return ResultDTO.success(virtualProductService.updateVirtualProduct(form, new LogTrackNumDto(form.getId())));
        } catch (NullPointerException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("update/virtualProduct/borrowingStrategy")
    @ResponseBody
    public ResultDTO<Boolean> updateVirtualBorrowingStrategy(@RequestBody VirtualUpdateForm form) {
        try {
            return ResultDTO.success(virtualProductService.updateVirtualBorrowingStrategy(form, new LogTrackNumDto(form.getId())));
        } catch (NullPointerException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }

    @RequiresPermissions("product:virtualProduct:delete")
    @GetMapping("delete/virtualProduct")
    @ResponseBody
    public ResultDTO<Boolean> deleteVirtualProduct(@RequestParam("virtualProductId") String virtualProductId) {
        try {
            return ResultDTO.success(virtualProductService.deleteVirtualProduct(virtualProductId, new LogTrackNumDto(virtualProductId)));
        } catch (NullPointerException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }

    @RequiresPermissions("product:virtualProduct:delete")
    @PostMapping("delete/virtualProductBatch")
    @ResponseBody
    public ResultDTO<Boolean> deleteVirtualProductBatch(@RequestBody VirtualProductSearchForm form) {
        try {
            return ResultDTO.success(virtualProductService.deleteVirtualProductBatch(form.getVirtualSkuIdList(), new LogTrackNumMapDto()));
        } catch (NullPointerException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/import/product")
    public ResultDTO<String> importProductInfo(@RequestParam("file") MultipartFile file) {
        log.info("导入虚拟商品-------------准备开始导入日志");
        virtualProductService.importVirtualProductInfo(file);
        log.info("导入虚拟商品-------------导入成功，返回消息至前端");
        return ResultDTO.success("正在导入");
    }

    /***************************************************************************************
     ******************************** 拆分成多接口易于控制权限 ********************************
     ***************************************************************************************/
    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/update/product/productType")
    public ResultDTO<String> updateProductType(@RequestParam("file") MultipartFile file) {
        log.info("更新虚拟商品{}-------------准备开始导入", productType.getDesc());
        virtualProductService.updateVirtualProductInfoPartly(file, productType);
        log.info("更新虚拟商品{}-------------导入成功，返回消息至前端", productType.getDesc());
        return ResultDTO.success("正在导入");
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/update/product/productStatus")
    public ResultDTO<String> updateProductStatus(@RequestParam("file") MultipartFile file) {
        log.info("更新虚拟商品{}-------------准备开始导入", productStatus.getDesc());
        virtualProductService.updateVirtualProductInfoPartly(file, productStatus);
        log.info("更新虚拟商品{}-------------导入成功，返回消息至前端", productStatus.getDesc());
        return ResultDTO.success("正在导入");
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/update/product/subType")
    public ResultDTO<String> updateProductSubType(@RequestParam("file") MultipartFile file) {
        log.info("更新虚拟商品{}-------------准备开始导入", subType.getDesc());
        virtualProductService.updateVirtualProductInfoPartly(file, subType);
        log.info("更新虚拟商品{}-------------导入成功，返回消息至前端", subType.getDesc());
        return ResultDTO.success("正在导入");
    }

    @RequiresPermissions("product:virtualProduct:borrowingStrategy")
    @PostMapping("/update/product/borrowingStrategy")
    public ResultDTO<String> updateBorrowingStrategy(@RequestParam("file") MultipartFile file) {
        virtualProductService.updateVirtualBorrowingStrategy(file);
        return ResultDTO.success("正在导入");
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/update/product/operator")
    public ResultDTO<String> updateProductOperator(@RequestParam("file") MultipartFile file) {
        log.info("更新虚拟商品{}-------------准备开始导入", operator.getDesc());
        virtualProductService.updateVirtualProductInfoPartly(file, operator);
        log.info("更新虚拟商品{}-------------导入成功，返回消息至前端", operator.getDesc());
        return ResultDTO.success("正在导入");
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/update/product/upgrade")
    public ResultDTO<String> updateProductUpgrade(@RequestParam("file") MultipartFile file) {
        virtualProductService.updateVirtualProductUpgrade(file);
        return ResultDTO.success("正在导入");
    }

    @RequiresPermissions("product:virtualProduct:list")
    @GetMapping("/info/product/shippingRatio/{virtualId}")
    public ResultDTO<SenboWarehouseVo> getVirtualShippingRatio(@PathVariable String virtualId) {
        return ResultDTO.success(virtualProductService.getVirtualShippingRatio(virtualId));
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/update/product/shippingRatio")
    public ResultDTO<Boolean> getVirtualShippingRatio(@RequestBody @Valid List<VirtualShippingRatioDto> list) {
        return ResultDTO.success(virtualProductService.updateVirtualShippingRatio(list, new LogTrackNumDto(list.getFirst().getVirtualSkuId())));
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/update/product/shippingRatio/import")
    public ResultDTO<String> importProductShippingRatio(@RequestParam("file") MultipartFile file) {
        virtualProductService.importProductShippingRatio(file);
        return ResultDTO.success("正在导入");
    }

    @PostMapping("/download/product/shippingRatioTemplate")
    public void shippingRatioTemplate(HttpServletResponse response) {
        virtualProductService.shippingRatioTemplate(response);
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("/info/upgrade")
    public ResultDTO<UpgradeInfoVo> getUpgradeInfo(@RequestBody UpgradeProductForm form) {
        return ResultDTO.success(virtualProductService.getUpgradeInfo(form));
    }

    @RequiresPermissions("product:virtualProduct:upgrade")
    @PostMapping("/update/upgrade")
    public ResultDTO<UpgradeInfoVo> updateUpgradeInfo(@RequestBody UpgradeProductForm form) {
        virtualProductService.updateUpgradeInfo(form);
        return ResultDTO.success();
    }

    /**
     * 导出
     * @param form
     * @param response
     */
    @RequiresPermissions("product:virtualProduct:export")
    @PostMapping("/export")
    public void exportRepInfo(@RequestBody VirtualProductSearchForm form, HttpServletResponse response) {
        virtualProductService.exportInfo(form, response);
    }

    /**
     * 获取产品品类列表
     */
    @RequiresPermissions("product:virtualProduct:list")
    @GetMapping("/category")
    public ResultDTO<List<ProductCategoryDTO>> categoryList() {
        return ResultDTO.success(virtualProductService.category());
    }
}
