package com.product.product_server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.common.entity.ChannelSearchDto;
import com.crafts_mirror.utils.common.entity.ChannelSearchVo;
import com.crafts_mirror.utils.dp.CalculationForm;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.crafts_mirror.utils.utils.PatternUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.google.common.base.Stopwatch;
import com.product.product_server.assembler.VirtualProductAssembler;
import com.product.product_server.entity.LogTrackNumMapDto;
import com.product.product_server.entity.dataObject.*;
import com.product.product_server.entity.dto.*;
import com.product.product_server.entity.excelObject.VirtualProductInfoExcel;
import com.product.product_server.entity.form.*;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.entity.vo.*;
import com.product.product_server.enums.BorrowingStrategyEnum;
import com.product.product_server.enums.UpgradeTypeEnum;
import com.product.product_server.enums.VirtualProductTypeEnum;
import com.product.product_server.enums.VirtualSubTypeEnum;
import com.product.product_server.exception.BusinessException;
import com.product.product_server.model.products.*;
import com.product.product_server.model.virtualProduct.VirtualProductInfoDp;
import com.product.product_server.repository.*;
import com.product.product_server.repository.interiorRepository.WarehouseRepository;
import com.product.product_server.service.*;
import com.product.product_server.utils.easyExcelUtil.listener.AbstractUpdateVirtualProductInfoListener;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SystemConstant.*;

/**
 * @Description 虚拟sku商品业务编排层
 * <AUTHOR>
 * @Date 2023/12/12 19:46
 **/
@Service
@Slf4j
public class VirtualProductServiceImpl implements IVirtualProductService {

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource
    private SelfProductRepositoryImpl selfProductRepository;

    @Resource
    private SpuProductRepositoryImpl spuProductRepository;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private FactoryInfoRepositoryImpl factoryInfoRepository;

    @Resource
    private IProductImportService productImportService;

    @Resource
    private IProductSnapshotService productSnapshotService;

    @Resource
    private ISysUserInteriorService sysUserInteriorService;

    @Resource
    private VirtualShippingRatioRepositoryImpl virtualShippingRatioRepository;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private VirtualProductAssembler virtualProductAssembler;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    private IUpgradeProductService upgradeProductService;

    @Resource
    private VirtualUpgradeRelationRepositoryImpl upgradeRelationRepository;

    @Resource
    private IProductCategoryService productCategoryService;

    @Resource
    private IChannelService channelService;

    @Override
    public boolean checkOldSkuExisted(String oldSku) {
        if (StrUtil.isBlank(oldSku)) {
            return false;
        }
        if (oldSku.length() > 20) {
            throw new IllegalArgumentException("老sku长度超出限制");
        } else if (!PatternUtils.UPPERCASE_LOWERCASE_NUMBER_DOT_PATTERN.matcher(oldSku).matches()) {
            throw new IllegalArgumentException("虚拟sku只能英文、数字和符号的组合");
        }
        return virtualProductRepository.checkOldSkuVirtualExisted(Wrappers.<VirtualProductDO>lambdaQuery()
                .eq(VirtualProductDO::getOldSku, oldSku));
    }

    @Override
    public boolean checkVirtualSkuExisted(String virtualSku) {
        if (StrUtil.isBlank(virtualSku)) {
            throw new IllegalArgumentException("虚拟sku不能为空");
        }
        if (virtualSku.length() > 20) {
            throw new IllegalArgumentException("虚拟sku长度超出限制");
        } else if (!PatternUtils.UPPERCASE_LOWERCASE_NUMBER_DOT_PATTERN.matcher(virtualSku).matches()) {
            throw new IllegalArgumentException("虚拟sku只能英文、数字和符号的组合");
        }
        return virtualProductRepository.checkOldSkuVirtualExisted(Wrappers.<VirtualProductDO>lambdaQuery()
                .eq(VirtualProductDO::getVirtualSku, virtualSku));
    }

    @Override
    @OperationLog(content = "创建虚拟sku", operationType = "自定义产品编辑")
    public boolean saveVirtualProduct(List<VirtualSkuProductSaveDto> dtoList, LogTrackNumDto logDto, String userName) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return true;
        }
        long uniqueVirtualSkuCount = dtoList.stream().map(VirtualSkuProductSaveDto::getVirtualSku).distinct().count();
        if (dtoList.size() != uniqueVirtualSkuCount) {
            throw new IllegalArgumentException("虚拟sku不能填相同的值");
        }
        long oldSkuCount = dtoList.stream().filter(info -> StrUtil.isNotBlank(info.getOldSku())).count();
        long uniqueOldSkuCount = dtoList.stream().map(VirtualSkuProductSaveDto::getOldSku).filter(StrUtil::isNotBlank).distinct().count();
        if (oldSkuCount != uniqueOldSkuCount) {
            throw new IllegalArgumentException("老sku不能填相同的值");
        }
        String virtualSkuExistedStr = null;
        String oldSkuExistedStr = null;
        for (VirtualSkuProductSaveDto dto : dtoList) {
            boolean virtualSkuExisted = checkVirtualSkuExisted(dto.getVirtualSku());
            boolean oldSkuExisted = checkOldSkuExisted(dto.getOldSku());
            if (virtualSkuExisted) {
                virtualSkuExistedStr = dto.getVirtualSku() + "，";
            }
            if (oldSkuExisted) {
                oldSkuExistedStr = dto.getOldSku() + ",";
            }
        }

        if (StrUtil.isNotBlank(virtualSkuExistedStr) || StrUtil.isNotBlank(oldSkuExistedStr)) {
            String existedSkuStr = String.format("虚拟sku：%s老sku：%s 重复，请修改这些重复数据",
                    Optional.ofNullable(virtualSkuExistedStr).orElse("无重复"),
                    Optional.ofNullable(oldSkuExistedStr).orElse("无重复"));
            throw new RuntimeException(existedSkuStr);
        }

        List<VirtualProductDO> productList = dtoList.stream()
                .map(dto -> convertVirtualProduct(dto, userName))
                .toList();

        return virtualProductRepository.saveVirtualProduct(productList);
    }

    @Override
    public ChannelSearchVo getNotExistedChannelSet(List<String> selfSkuList) {
        ChannelSearchVo channelSearchVo = channelService.getAllNicheChannel();
        List<ChannelSearchDto> channelSearchList = channelSearchVo.getChannelSearchDtoList();
        if (CollectionUtil.isEmpty(channelSearchList)) {
            channelSearchVo.setChannelSearchDtoList(new ArrayList<>());
            return channelSearchVo;
        }
        Set<String> channelSetOfProduct = getAllChannelSetOfSelfProduct(selfSkuList);
        channelSearchList = channelSearchList.stream()
                .filter(f -> !channelSetOfProduct.contains(f.getChannelId()))
                .toList();
        channelSearchVo.setChannelSearchDtoList(channelSearchList);
        return channelSearchVo;
    }

    @Override
    public ChannelSearchVo getExistedChannelSet(List<String> selfSkuList) {
        ChannelSearchVo channelSearchVo = channelService.getAllNicheChannel();
        List<ChannelSearchDto> channelSearchList = channelSearchVo.getChannelSearchDtoList();
        if (CollectionUtil.isEmpty(channelSearchList)) {
            channelSearchVo.setChannelSearchDtoList(new ArrayList<>());
            return channelSearchVo;
        }
        Set<String> channelSetOfProduct = getAllChannelSetOfSelfProduct(selfSkuList);
        channelSearchList = channelSearchList.stream()
                .filter(f -> channelSetOfProduct.contains(f.getChannelId()))
                .toList();
        channelSearchVo.setChannelSearchDtoList(channelSearchList);
        return channelSearchVo;
    }

    private Set<String> getAllChannelSetOfSelfProduct(List<String> selfSkuList) {
        // 获取所有该自定义sku下的的渠道
        List<VirtualProductDO> allVirtualProductList = virtualProductRepository.getAllVirtualProduct(selfSkuList);
        if (CollectionUtil.isEmpty(allVirtualProductList)) {
            return new HashSet<>();
        }
        // 过滤已创建虚拟sku的渠道
        return allVirtualProductList.stream().map(VirtualProductDO::getChannel).collect(Collectors.toSet());
    }

    @Override
    public IPage<VirtualProductListVo> pageList(VirtualProductSearchForm form) {
        // 根据搜索条件获取虚拟sku列表
        IPage<VirtualProductDO> virtualProductPage = virtualProductRepository.getVirtualProductPage(form);
        List<VirtualProductDO> records = virtualProductPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return new Page<>(virtualProductPage.getCurrent(), virtualProductPage.getSize(), virtualProductPage.getTotal());
        }

        List<VirtualProductListVo> virtualProductVoList = getVirtualProductInfoList(records);

        IPage<VirtualProductListVo> page = new Page<>(virtualProductPage.getCurrent(), virtualProductPage.getSize(), virtualProductPage.getTotal());
        page.setRecords(virtualProductVoList);
        return page;
    }

    @Override
    public VirtualProductDetailVo getDetail(String virtualProductId) {
        VirtualProductDO virtualProduct = virtualProductRepository.virtualProductDetail(virtualProductId);
        if (virtualProduct == null) {
            return new VirtualProductDetailVo();
        }
        // 渠道
        Map<String, String> channelIdNameMap = channelService.getChannelIdNameMap();
        virtualProduct.setChannel(channelIdNameMap.getOrDefault(virtualProduct.getChannel(), virtualProduct.getChannel()));
        // 获取自定义sku相关信息
        SelfProductDO selfProductDO = selfProductRepository.productDetailBySelfSku(virtualProduct.getSelfProductSkuId());
        // 获取父spu相关信息
        SpuProductDO spuProductDO = spuProductRepository.productDetail(virtualProduct.getSpuId());
        // 获取供应商相关信息
        FactoryInfoDO factoryInfoDO = factoryInfoRepository.getById(selfProductDO.getFactoryId());
        // 获取发货比例相关信息
        List<SenboWarehouseDto> senboWarehouseWithoutMTList = warehouseRepository.getSenboWarehouseWithoutMTList();
        List<VirtualShippingRatioDO> virtualShippingRatioList = virtualShippingRatioRepository.getVirtualShippingRatioList(Collections.singletonList(virtualProduct.getId()));
        Map<Integer, Double> collect = virtualShippingRatioList.stream().collect(Collectors.toMap(VirtualShippingRatioDO::getWarehouseId, VirtualShippingRatioDO::getShippingRatio));
        List<VirtualShippingRatioVo> virtualShippingRatioVos = new ArrayList<>();

        senboWarehouseWithoutMTList.forEach(i -> {
            BigDecimal ratio = BigDecimal.valueOf(collect.getOrDefault(i.getSenboWarehouseId(), 0.0));
            BigDecimal result = ratio.multiply(BigDecimal.valueOf(100)).setScale(1, RoundingMode.HALF_UP);
            i.setShippingRatio(result.doubleValue());
            // 修改这里以去除不必要的小数部分
            String formattedResult = result.stripTrailingZeros().toPlainString();
            if (formattedResult.endsWith(".")) {
                formattedResult = formattedResult.substring(0, formattedResult.length() - 1);
            }
            virtualShippingRatioVos.add(VirtualShippingRatioVo.builder()
                    .warehouse(i.getSenboWarehouse())
                    .warehouseId(i.getSenboWarehouseId())
                    .headShippingDate(i.getHeadShippingDate())
                    .shippingRatio(result.compareTo(BigDecimal.ZERO) == 0 ? "0%" : formattedResult + "%").build());
        });

        virtualShippingRatioVos.sort((a, b) -> {
            double ratioA = Double.parseDouble(a.getShippingRatio().replace("%", ""));
            double ratioB = Double.parseDouble(b.getShippingRatio().replace("%", ""));
            return Double.compare(ratioB, ratioA); // 降序排列
        });

        return VirtualProductDetailVo.convertProductDetail(selfProductDO, virtualProduct, Optional.ofNullable(spuProductDO).orElse(new SpuProductDO()), factoryInfoDO, virtualShippingRatioVos);
    }

    @Override
    public IPage<VirtualProductForSpuVo> pageListForSpu(VirtualProductSearchForSpuForm form) {
        return virtualProductRepository.getVirtualProductPage(form);
    }

    @Override
    public List<VirtualProductListVo> getVirtualProductListUnderSpu(VirtualSkuInfoUnderSpuForm form) {
        List<VirtualProductDO> virtualProductList = virtualProductRepository.getVirtualProductListUnderSpu(form);
        return getVirtualProductInfoList(virtualProductList);
    }

    @Override
    @OperationLog(content = "更新虚拟产品", operationType = "虚拟产品编辑")
    public boolean updateVirtualProduct(VirtualUpdateForm form, LogTrackNumDto trackNumDto) {
        VirtualUpdateDp updateDp = new VirtualUpdateDp(form.getId(), form.getRemarks(), form.getSubType(), form.getProductStatus(),
                form.getProductType(), SecurityUtils.getUsername(), null);
        boolean updated = virtualProductRepository.updateVirtualProduct(updateDp) == 1;
        if (updated) {
            productSnapshotService.saveProductSnapshotList(Collections.singletonList(form.getId()));
        }
        return updated;
    }

    @Override
    @OperationLog(content = "借货策略调整", operationType = "虚拟产品编辑")
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVirtualBorrowingStrategy(VirtualUpdateForm form, LogTrackNumDto trackNumDto) {
        boolean update = virtualProductRepository.update(Wrappers.<VirtualProductDO>lambdaUpdate()
                .set(VirtualProductDO::getBorrowingStrategy, form.getBorrowingStrategy())
                .set(VirtualProductDO::getUpdateBy, SecurityUtils.getUsername())
                .set(VirtualProductDO::getUpdateDate, new Date())
                .eq(VirtualProductDO::getId, form.getId()));
        if (update) {
            productSnapshotService.saveProductSnapshotList(Collections.singletonList(form.getId()));
        }
        return update;
    }

    @Override
    @OperationLog(content = "删除虚拟sku", operationType = "虚拟产品编辑")
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVirtualProduct(String virtualProductId, LogTrackNumDto dto) {

        InventoryInfoQuery inventoryInfoQuery = new InventoryInfoQuery();
        inventoryInfoQuery.setVirtualSkuIdList(Collections.singletonList(virtualProductId));

        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.post(inventoryInfoQuery, ResultDTO.class, INVENTORY_SYSTEM_HAS_REDUNDANCY_VIRTUAL_SKU_ID);
        Boolean hasRedundancy = JSON.to(Boolean.class, resultDTO.getData());
        if (hasRedundancy) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "先在库存管理删除该产品库存后再删除产品");
        }

        VirtualProductDO virtualProductDO = virtualProductRepository.getById(virtualProductId);
        if (ObjectUtil.isEmpty(virtualProductDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "虚拟产品不存在");
        }
        // 删除升级款
        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())) {
            upgradeProductService.deleteUpgradeRelation(virtualProductDO.getUpgradeId());
        }
        boolean deleteVirtualProduct = virtualProductRepository.deleteVirtualProductByVirtualIds(Collections.singletonList(virtualProductId));

        boolean deleteSelfProductBool = false;
        boolean deleteSpuProduct = false;
        if (deleteVirtualProduct) {
            // 判断该自定义商品下还有没有虚拟商品，没有的话需要同步删除自定义sku
            String selfProductSkuId = virtualProductDO.getSelfProductSkuId();
            long countSelfProduct = virtualProductRepository.countBySelfSku(selfProductSkuId);

            if (countSelfProduct == 0) {
                int deleteSelfProduct = selfProductRepository.deleteSelfProduct(selfProductSkuId);
                deleteSelfProductBool = deleteSelfProduct > 0;
            } else {
                // 如果不需要删除自定义sku的话，直接将删除标记置为true
                deleteSelfProductBool = true;
            }

            // 判断该spu下还有没有虚拟商品，如果没有的话需要同步删除spu
            String spuId = virtualProductDO.getSpuId();
            long countSpu = virtualProductRepository.countBySpu(spuId);
            if (countSpu == 0) {
                deleteSpuProduct = spuProductRepository.deleteSpuProduct(spuId);
            } else {
                // 如果不需要删除spu的话，直接将删除标记置为true
                deleteSpuProduct = true;
            }

            // 同步需要删除目标日销的数据
            ResultDTO deleteTargetSalesResult = restTemplateUtil.get(String.format(DELETE_TARGET_SALES + "?virtualId=%s", virtualProductId), ResultDTO.class);
            Boolean delete = JSON.to(Boolean.class, deleteTargetSalesResult.getData());
            if (!delete) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "先在库存管理删除该产品库存后再删除产品");
            }

        }

        return deleteSelfProductBool && deleteSpuProduct;
    }

    @Override
    @OperationLog(content = "删除虚拟sku", operationType = "虚拟产品编辑")
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVirtualProductBatch(List<String> virtualProductIdList, LogTrackNumMapDto dto) {

        InventoryInfoQuery inventoryInfoQuery = new InventoryInfoQuery();
        inventoryInfoQuery.setVirtualSkuIdList(virtualProductIdList);

        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.post(inventoryInfoQuery, ResultDTO.class, INVENTORY_SYSTEM_HAS_REDUNDANCY_VIRTUAL_SKU_ID_LIST);
        List<String> hasRedundancyList = JSON.to(List.class, resultDTO.getData());
        if (CollectionUtil.isNotEmpty(hasRedundancyList)) {
            List<VirtualProductDO> virtualProductDOList = virtualProductRepository.listByIds(hasRedundancyList);
            if(CollectionUtil.isNotEmpty(virtualProductDOList) && virtualProductDOList.size() > 3) {
                // 删除第三个之后的数据
                virtualProductDOList = virtualProductDOList.subList(0, 3);
            }
            String virtualSkuStr = virtualProductDOList.stream().map(VirtualProductDO::getVirtualSku).collect(Collectors.joining(","));
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "先在库存管理删除该产品库存后再删除产品："+virtualSkuStr);
        }

        List<VirtualProductDO> virtualProductDOList = virtualProductRepository.listByIds(virtualProductIdList);
        List<VirtualProductDO> upgradeList = virtualProductDOList.stream().filter(i -> StrUtil.isNotBlank(i.getUpgradeId())).collect(Collectors.toList());
        // 删除升级款
        if (CollectionUtil.isNotEmpty(upgradeList)) {
            upgradeProductService.deleteUpgradeRelationList(upgradeList.stream().map(VirtualProductDO::getUpgradeId).collect(Collectors.toList()));
        }
        boolean deleteVirtualProduct = virtualProductRepository.deleteVirtualProductByVirtualIds(virtualProductIdList);

        if (deleteVirtualProduct) {
            Map<String, String> logMap = new HashMap<>();
            // 判断该自定义商品下还有没有虚拟商品，没有的话需要同步删除自定义sku
            Set<String> selfProductSkuIds = new HashSet<>();
            Set<String> spuIds = new HashSet<>();
            for (VirtualProductDO virtualProductDO : virtualProductDOList) {
                String selfProductSkuId = virtualProductDO.getSelfProductSkuId();
                long countSelfProduct = virtualProductRepository.countBySelfSku(selfProductSkuId);

                if (countSelfProduct == 0) {
                    selfProductSkuIds.add(selfProductSkuId);
                }
                String spuId = virtualProductDO.getSpuId();

                long countSpu = virtualProductRepository.countBySpu(spuId);
                if (countSpu == 0) {
                    spuIds.add(spuId);
                }
                logMap.put(virtualProductDO.getId(), "删除虚拟sku");
            }
            dto.setLogMap(logMap);
            dto.setAuthorization(SecurityUtils.getToken());
            if (CollectionUtil.isNotEmpty(selfProductSkuIds)) {
                selfProductRepository.removeBatchByIds(selfProductSkuIds);
            }
            if (CollectionUtil.isNotEmpty(spuIds)) {
                spuProductRepository.removeBatchByIds(spuIds);
            }
            // 同步需要删除目标日销的数据
            CalculationForm form = CalculationForm.builder().virtualSkuIdList(virtualProductIdList).build();
            RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
            ResultDTO<Boolean> deleteTargetSalesResult = restTemplateUtils.post(form, ResultDTO.class, DELETE_TARGET_SALES);
            Boolean delete = JSON.to(Boolean.class, deleteTargetSalesResult.getData());
            if (!delete) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "先在库存管理删除该产品库存后再删除产品");
            }

        }
        return Boolean.TRUE;
    }

    @Override
    @Async
    public void importVirtualProductInfo(MultipartFile file) {
        try {
            productImportService.importExcelToVirtualProduct(file.getInputStream(), file.getBytes(), file.getOriginalFilename());
        } catch (IOException e) {
            log.error("导入虚拟商品excel文件异常：", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void updateVirtualProductInfoPartly(MultipartFile file, AbstractUpdateVirtualProductInfoListener.UpdateType updateType) {
        try {
            productImportService.updateVirtualProductInfoPartly(file.getInputStream(), file.getBytes(), file.getOriginalFilename(), updateType);
        } catch (Exception e) {
            log.error(String.format("更新虚拟商品%s 文件异常：", updateType.getDesc()), e);
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), String.format("更新虚拟商品%s 文件异常：", updateType.getDesc()), e);
        }
    }

    @Override
    public void updateVirtualProductUpgrade(MultipartFile file) {
        try {
            productImportService.updateVirtualProductUpgrade(file.getInputStream(), file.getBytes(), file.getOriginalFilename());
        } catch (Exception e) {
            log.error("关联升级款产品文件异常", e);
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "关联升级款产品文件异常", e);
        }
    }

    @Override
    public void updateVirtualBorrowingStrategy(MultipartFile file) {
        try {
            productImportService.updateVirtualBorrowingStrategy(file.getInputStream(), file.getBytes(), file.getOriginalFilename());
        } catch (Exception e) {
            log.error("借货策略文件异常", e);
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "借货策略文件异常", e);
        }
    }

    @Override
    public SenboWarehouseVo getVirtualShippingRatio(String virtualId) {
        List<SenboWarehouseDto> senboWarehouseWithoutMTList = warehouseRepository.getSenboWarehouseWithoutMTList();

        List<VirtualShippingRatioDO> virtualShippingRatioList = virtualShippingRatioRepository.getVirtualShippingRatioList(Collections.singletonList(virtualId));
        Map<Integer, Double> warehouseRatioMap = virtualShippingRatioList.stream().collect(Collectors.toMap(VirtualShippingRatioDO::getWarehouseId, VirtualShippingRatioDO::getShippingRatio));

        for (SenboWarehouseDto i : senboWarehouseWithoutMTList) {
            BigDecimal ratio = BigDecimal.valueOf(warehouseRatioMap.getOrDefault(i.getSenboWarehouseId(), 0.0));
            BigDecimal result = ratio.multiply(BigDecimal.valueOf(100)).setScale(1, RoundingMode.HALF_UP);
            // 去除小数部分为0的情况
            String formattedResult = result.stripTrailingZeros().toPlainString();
            if (formattedResult.endsWith(".")) {
                formattedResult = formattedResult.substring(0, formattedResult.length() - 1);
            }
            i.setShippingRatio(Double.parseDouble(formattedResult));
        }
        List<Integer> virtualShippingRatioDtoList = virtualShippingRatioList.stream().map(VirtualShippingRatioDO::getWarehouseId).toList();
        return SenboWarehouseVo.builder().senboWarehouseList(senboWarehouseWithoutMTList).virtualShippingRatioDtoList(virtualShippingRatioDtoList).build();
    }

    /**
     * 更新发货比例信息
     *
     * @param list 发货比例数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(content = "更新发货比例", operationType = "虚拟产品编辑")
    public boolean updateVirtualShippingRatio(List<VirtualShippingRatioDto> list, LogTrackNumDto logDto) {
        List<VirtualShippingRatioDO> virtualShippingRatioDOList = virtualProductAssembler.virtualShippingRatioDtoListToDOList(list);

        for (VirtualShippingRatioDO ratio : virtualShippingRatioDOList) {
            Double shippingRatio = ratio.getShippingRatio();
            if (shippingRatio == null || shippingRatio < 0 || !isValidDecimalPlaces(shippingRatio, 1)) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "发货比例必须大于0,且小数位数不超过1位");
            }
        }

        boolean isValid = virtualShippingRatioDOList.stream()
                .map(vo -> BigDecimal.valueOf(vo.getShippingRatio()))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .compareTo(BigDecimal.valueOf(100)) == 0;
        if (!isValid) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "发货比例合计不等于100%");
        }
        virtualShippingRatioRepository.deleteVirtualShippingRatio(list.getFirst().getVirtualSkuId());
        return virtualShippingRatioRepository.saveBatch(virtualShippingRatioDOList.stream()
                .filter(vo -> vo.getShippingRatio() > 0)
                .peek(vo -> {
                    BigDecimal ratio = BigDecimal.valueOf(vo.getShippingRatio());
                    BigDecimal result = ratio.divide(BigDecimal.valueOf(100), 3, RoundingMode.HALF_UP);
                    vo.setShippingRatio(result.doubleValue());
                }).toList());
    }

    // 辅助方法:检查小数位数
    private boolean isValidDecimalPlaces(Double value, int maxDecimalPlaces) {
        String[] parts = value.toString().split("\\.");
        return parts.length == 1 || parts[1].length() <= maxDecimalPlaces;
    }

    @Override
    public void importProductShippingRatio(MultipartFile file) {
        try {
            productImportService.importProductShippingRatio(file.getInputStream(), file.getBytes(), file.getOriginalFilename());
        } catch (IOException e) {
            log.error("导入虚拟商品excel文件异常：", e);
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), e.getMessage());
        }
    }

    @Override
    public void shippingRatioTemplate(HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("批量编辑发货仓库模板", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            List<List<String>> head = new ArrayList<>();
            List<String> list0 = new ArrayList<>();
            list0.add("* 虚拟SKU");
            list0.add("* 虚拟SKU");
            head.add(list0);
            List<SenboWarehouseDto> senboWarehouseWithoutMTList = warehouseRepository.getSenboWarehouseWithoutMTList();
            senboWarehouseWithoutMTList.forEach(i -> {
                List<String> list = new ArrayList<>();
                list.add("* 发货比例");
                list.add(i.getSenboWarehouse());
                head.add(list);
            });
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();

            // 头的策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();

            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short) 12);
            headWriteCellStyle.setWriteFont(headWriteFont);

            HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                    new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

            WriteSheet writeSheet1 = EasyExcel.writerSheet(1).sheetName("sheet").head(head).automaticMergeHead(true)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(horizontalCellStyleStrategy).build();
            excelWriter.write(new ArrayList<>(), writeSheet1);

        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    @Override
    public List<VirtualProductInfoDp> getVirtualProductInfoListByVirtualId(List<String> virtualIdList) {
        return virtualProductRepository.getVirtualProductInfoListByVirtualId(virtualIdList);
    }

    @Override
    public List<VirtualProductListVo> selectListByForm(VirtualProductSearchForm form) {
        // 根据搜索条件获取虚拟sku列表
        List<VirtualProductDO> virtualProductList = virtualProductRepository.getVirtualProductList(form);
        List<VirtualProductListVo> virtualProductVoList = new ArrayList<>();

        if (CollectionUtil.isEmpty(virtualProductList)) {
            return virtualProductVoList;
        }
        Map<String, SelfProductDO> selfProductMap = new HashMap<>(16);
        Map<String, ProductCategoryDTO> productCategoryDTOMap = productCategoryService.selectCategoryLeafTree();

        // 渠道
        Map<String, String> channelIdNameMap = channelService.getChannelIdNameMap();

        // 根据虚拟sku列表获取自定义sku相关信息
        for (VirtualProductDO virtualProduct : virtualProductList) {
            String selfSkuId = virtualProduct.getSelfProductSkuId();
            // 获取自定义商品相关信息并缓存
            SelfProductDO selfProduct = selfProductMap.getOrDefault(selfSkuId, selfProductRepository.getSelfProductBySkuId(selfSkuId));
            // 如果没有对应的自定义sku的话，该数据有问题，跳过不处理
            if (Objects.isNull(selfProduct)) {
                log.error("虚拟sku “{}” 商品缺少对应的自定义sku，搜索列表页时已跳过，请确认数据情况", virtualProduct.getVirtualSku());
                continue;
            }
            FactoryInfoDO factoryInfoDO = factoryInfoRepository.getById(selfProduct.getFactoryId());
            selfProductMap.putIfAbsent(selfSkuId, selfProduct);

            ProductCategoryDTO parentDTO = productCategoryDTOMap.getOrDefault(String.valueOf(selfProduct.getCategoryId()), null);
            String parentName = null;
            if (parentDTO != null) {
                parentName = parentDTO.getCategoryName();
            }

            // 获取返回给前端的数据
            virtualProductVoList.add(convertToVirtualProductListVo(selfProduct.getSku(), selfProduct, virtualProduct,
                    factoryInfoDO, parentName, channelIdNameMap, new SpuProductDO()));
        }
        return virtualProductVoList;
    }

    @Override
    public UpgradeInfoVo getUpgradeInfo(UpgradeProductForm form) {

        VirtualProductDO virtualProductDO = virtualProductRepository.getById(form.getVirtualSkuId());
        if (ObjectUtil.isEmpty(virtualProductDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "虚拟产品不存在");
        }
        if (StrUtil.isBlank(virtualProductDO.getUpgradeId())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "升级款不存在");
        }

        UpgradeInfoDto upgradeInfo = upgradeProductService.getUpgradeInfo(virtualProductDO.getId(), virtualProductDO.getUpgradeId());
        return UpgradeInfoVo.builder().upgradeSkuId(upgradeInfo.getUpgradeSkuId()).upgradeSku(upgradeInfo.getUpgradeSku()).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUpgradeInfo(UpgradeProductForm form) {
        VirtualProductDO virtualProductDO = virtualProductRepository.getById(form.getVirtualSkuId());
        if (ObjectUtil.isEmpty(virtualProductDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "虚拟产品不存在");
        }
        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())) {
            upgradeProductService.deleteUpgradeRelation(virtualProductDO.getUpgradeId());
        }
    }

    @Override
    public UpgradeInfoInteriorVo selectUpgradeInfo(UpgradeProductForm form) {
        VirtualUpgradeRelationDO upgradeRelationDO = upgradeRelationRepository.getById(form.getId());

        String originalId = upgradeRelationDO.getOriginalId();
        String upgradeId = upgradeRelationDO.getUpgradeId();
        String originalSku = virtualProductRepository.getById(originalId).getVirtualSku();
        String upgradeSku = virtualProductRepository.getById(upgradeId).getVirtualSku();
        return UpgradeInfoInteriorVo.builder()
                .originalSkuId(originalId)
                .originalSku(originalSku)
                .upgradeSkuId(upgradeId)
                .upgradeSku(upgradeSku)
                .build();
    }

    @Override
    public void exportInfo(VirtualProductSearchForm form, HttpServletResponse response) {
        List<VirtualProductInfoExcel> exportInfo = virtualProductRepository.getExportInfo(form);
        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        Map<String, String> channelIdNameMap = channelService.getChannelIdNameMap();

        for (var excel : exportInfo) {
            String productStatus = excel.getProductStatus();
            if (StrUtil.isNotBlank(productStatus)) {
                VirtualProductStatusEnum statusEnum = VirtualProductStatusEnum.ofCode(Integer.valueOf(productStatus));
                excel.setProductStatus(statusEnum != null ? statusEnum.getDesc() : productStatus);
            }
            String productType = excel.getProductType();
            if (StrUtil.isNotBlank(productType)) {
                VirtualProductTypeEnum typeEnum = VirtualProductTypeEnum.ofCode(Integer.valueOf(productType));
                excel.setProductType(typeEnum != null ? typeEnum.getDesc() : productType);
            }
            String subType = excel.getSubType();
            if (StrUtil.isNotBlank(subType)) {
                VirtualSubTypeEnum subTypeEnum = VirtualSubTypeEnum.ofCode(Integer.valueOf(subType));
                excel.setSubType(subTypeEnum != null ? subTypeEnum.getDesc() : subType);
            }
            String operator = excel.getOperator();
            if (StrUtil.isNotBlank(operator)) {
                String operatorNames = Arrays.stream(operator.split(",", 0))
                        .map(op -> StrUtil.isNotBlank(collect.get(op)) ? collect.get(op) : op)
                        .collect(Collectors.joining(","));
                excel.setOperator(operatorNames);
            }
            if (StrUtil.isNotBlank(excel.getBorrowingStrategy())) {
                BorrowingStrategyEnum borrowingStrategyEnum = BorrowingStrategyEnum.ofCode(Integer.valueOf(excel.getBorrowingStrategy()));
                excel.setBorrowingStrategy(borrowingStrategyEnum != null ? borrowingStrategyEnum.getDesc() : null);
            }

            if (StrUtil.isNotBlank(excel.getChannel())) {
                excel.setChannel(channelIdNameMap.getOrDefault(excel.getChannel(), excel.getChannel()));
            }
        }
        ExcelWriter excelWriter = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("虚拟产品导出", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            WriteSheet writeSheet = EasyExcel.writerSheet(1, "虚拟SKU").head(VirtualProductInfoExcel.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();

            excelWriter.write(exportInfo, writeSheet);
        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        log.info("导出虚拟产品:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

    private List<VirtualProductListVo> getVirtualProductInfoList(List<VirtualProductDO> records) {
        List<VirtualProductListVo> virtualProductVoList = new ArrayList<>(20);
        Map<String, SelfProductDO> selfProductMap = new HashMap<>(16);

        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        Set<String> spuIds = records.stream().map(VirtualProductDO::getSpuId).collect(Collectors.toSet());
        Map<String, SpuProductDO> spuMap = new HashMap<>();
        List<SpuProductDO> spuProductDOS = spuProductRepository.listByIds(spuIds);
        if (CollectionUtil.isNotEmpty(spuProductDOS)) {
            spuMap = spuProductDOS.stream().collect(Collectors.toMap(SpuProductDO::getId, spuProductDO -> spuProductDO));
        }
        // 渠道
        Map<String, String> channelIdNameMap = channelService.getChannelIdNameMap();

        Map<String, ProductCategoryDTO> productCategoryDTOMap = productCategoryService.selectCategoryLeafTree();
        // 根据虚拟sku列表获取自定义sku相关信息
        for (VirtualProductDO virtualProduct : records) {
            String selfSkuId = virtualProduct.getSelfProductSkuId();
            // 获取自定义商品相关信息并缓存
            SelfProductDO selfProduct = selfProductMap.getOrDefault(selfSkuId, selfProductRepository.getSelfProductBySkuId(selfSkuId));
            // 如果没有对应的自定义sku的话，该数据有问题，跳过不处理
            if (Objects.isNull(selfProduct)) {
                log.error("虚拟sku “{}” 商品缺少对应的自定义sku，搜索列表页时已跳过，请确认数据情况", virtualProduct.getVirtualSku());
                continue;
            }
            FactoryInfoDO factoryInfoDO = factoryInfoRepository.getById(selfProduct.getFactoryId());
            selfProductMap.putIfAbsent(selfSkuId, selfProduct);

            String createBy = collect.get(virtualProduct.getCreateBy());
            if (StrUtil.isNotBlank(createBy)) {
                virtualProduct.setCreateBy(createBy);
            }

            String buyer = collect.get(selfProduct.getBuyer());
            if (StrUtil.isNotBlank(buyer)) {
                selfProduct.setBuyer(buyer);
            }

            String operator = virtualProduct.getOperator();
            if (StrUtil.isNotBlank(operator)) {
                // 使用String.split()方法的新重载,直接返回Stream
                String operatorNames = Arrays.stream(operator.split(",", 0))
                        .map(op -> StrUtil.isNotBlank(collect.get(op)) ? collect.get(op) : op)
                        .collect(Collectors.joining(","));
                virtualProduct.setOperator(operatorNames);
            }
            ProductCategoryDTO parentDTO = productCategoryDTOMap.getOrDefault(String.valueOf(selfProduct.getCategoryId()), null);
            String parentName = null;
            if (parentDTO != null) {
                parentName = parentDTO.getCategoryName();
            }

            // 获取返回给前端的数据
            virtualProductVoList.add(convertToVirtualProductListVo(selfProduct.getSku(), selfProduct, virtualProduct,
                    factoryInfoDO, parentName, channelIdNameMap, spuMap.getOrDefault(virtualProduct.getSpuId(), new SpuProductDO())));
        }
        return virtualProductVoList;
    }

    private VirtualProductListVo convertToVirtualProductListVo(String selfSku, SelfProductDO selfProduct, VirtualProductDO virtualProduct,
                                                               FactoryInfoDO factoryInfoDO, String parentName, Map<String, String> channelIdNameMap, SpuProductDO spu) {
        Double priceWithTaxesDouble = selfProduct.getPriceWithTaxes();
        BigDecimal priceWithTaxes = null;
        if (priceWithTaxesDouble != null) {
            priceWithTaxes = BigDecimal.valueOf(priceWithTaxesDouble);
        }
        String upgradeType = null;
        String upgradeSkuId = null;
        if (StrUtil.isNotBlank(virtualProduct.getUpgradeId())) {
            VirtualUpgradeRelationDO upgradeDO = upgradeRelationRepository.getById(virtualProduct.getUpgradeId());
            if (ObjectUtil.isNotEmpty(upgradeDO)) {
                boolean flag = upgradeDO.getOriginalId().equals(virtualProduct.getId());
                upgradeType = flag ? UpgradeTypeEnum.ORIGINAL.getCode() : UpgradeTypeEnum.UPGRADE.getCode();
                upgradeSkuId = flag ? upgradeDO.getUpgradeId() : upgradeDO.getOriginalId();
            }
        }
        return VirtualProductListVo.builder()
                .virtualSkuId(virtualProduct.getId())
                .image(selfProduct.getImage())
                .buyer(selfProduct.getBuyer())
                .productName(selfProduct.getProductName())
                .selfProductId(selfProduct.getId())
                .sku(selfProduct.getSku())
                .selfProductSkuAndId(new SelfProductSkuAndId(selfSku, selfProduct.getId()))
                .virtualSku(virtualProduct.getVirtualSku())
                .oldSku(Optional.ofNullable(virtualProduct.getOldSku()).orElse("-"))
                .channel(channelIdNameMap.getOrDefault(virtualProduct.getChannel(), virtualProduct.getChannel()))
                .subType(virtualProduct.getSubType())
                .productType(virtualProduct.getProductType())
                .productStatus(virtualProduct.getProductStatus())
                .caseGrossWeight(selfProduct.getSingleCaseGrossWeight())
                .remarks(virtualProduct.getRemarks())
                .containerLoad(selfProduct.getContainerLoad())
                .factoryCode(factoryInfoDO.getFactoryCode())
                .factoryId(factoryInfoDO.getId())
                .purchaseInfoDp(
                        new PurchaseInfoDp(
                                new PurchaseDateDp(selfProduct.getPurchaseDate()),
                                selfProduct.getBuyer(),
                                selfProduct.getCurrency(),
                                new PriceDp(selfProduct.getTaxes(), BigDecimal.valueOf(selfProduct.getPrice()), priceWithTaxes),
                                factoryInfoDO.getFactoryCode())
                )
                .outCaseSpecification(
                        new SpecificationDp(selfProduct.getCaseLength(), selfProduct.getCaseWidth(), selfProduct.getCaseHeight())
                )
                .creatorEditorDp(new CreatorEditorDp(virtualProduct.getCreateBy(), virtualProduct.getUpdateBy(),
                        virtualProduct.getCreateDate(), virtualProduct.getUpdateDate()))
                .operatorList(virtualProduct.getOperator())
                .upgradeType(upgradeType)
                .upgradeSkuId(upgradeSkuId)
                .categoryName(parentName)
                .borrowingStrategy(virtualProduct.getBorrowingStrategy())
                .spu(spu.getSpu())
                .spuName(spu.getSpuProductName())
                .build();
    }

    private VirtualProductDO convertVirtualProduct(VirtualSkuProductSaveDto dto, String username) {
        return VirtualProductDO.builder()
                .selfProductSkuId(dto.getSelfProductSkuId())
                .channel(dto.getChannel())
                .virtualSku(dto.getVirtualSku())
                .oldSku(dto.getOldSku())
                .createBy(username)
                .updateBy(username)
                .build();
    }

    @Override
    public List<ProductCategoryDTO> category() {
        return productCategoryService.selectCategoryTree();
    }
}
