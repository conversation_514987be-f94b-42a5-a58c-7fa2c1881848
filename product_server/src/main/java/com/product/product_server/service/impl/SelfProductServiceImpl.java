package com.product.product_server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.dp.CalculationForm;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.UserPostEnum;
import com.crafts_mirror.utils.enums.YesOrNoEnum;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.google.common.base.Stopwatch;
import com.product.product_server.entity.LogTrackNumMapDto;
import com.product.product_server.entity.dataObject.FactoryInfoDO;
import com.product.product_server.entity.dataObject.SelfProductDO;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.dto.ProductCategoryDTO;
import com.product.product_server.entity.dto.SelfProductDto;
import com.product.product_server.entity.dto.VirtualSkuProductUpdateDto;
import com.product.product_server.entity.excelObject.SelfProductInfoExportExcel;
import com.product.product_server.entity.form.*;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.entity.vo.*;
import com.product.product_server.enums.*;
import com.product.product_server.exception.BusinessException;
import com.product.product_server.model.products.CreatorEditorDp;
import com.product.product_server.model.products.PurchaseInfoDp;
import com.product.product_server.model.products.RemarksSaveDp;
import com.product.product_server.model.virtualProduct.UpdateVirtualSkuDp;
import com.product.product_server.model.virtualProduct.VirtualSkuAndIdDp;
import com.product.product_server.repository.FactoryInfoRepositoryImpl;
import com.product.product_server.repository.SelfProductRepositoryImpl;
import com.product.product_server.repository.SpuProductRepositoryImpl;
import com.product.product_server.repository.VirtualProductRepositoryImpl;
import com.product.product_server.service.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SystemConstant.*;

/**
 * @Description 自定义产品业务编排层
 * <AUTHOR>
 * @Date 2023/12/6 14:17
 **/
@Service
@Slf4j
public class SelfProductServiceImpl implements ISelfProductService {

    @Resource
    private SelfProductRepositoryImpl productRepository;

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource
    private SpuProductRepositoryImpl spuProductRepository;

    @Resource
    private IProductImportService productImportService;

    @Resource
    private IProductSnapshotService productSnapshotService;

    @Resource
    private FactoryInfoRepositoryImpl factoryInfoRepository;

    @Resource
    private ISysUserInteriorService sysUserInteriorService;

    @Resource
    private IUpgradeProductService upgradeProductService;

    @Resource
    private IProductCategoryService productCategoryService;

    @Resource
    private IChannelService channelService;

    @Resource
    protected RestTemplate restTemplate;

    private static final Set<String> noPriceAuthorizationUsernameSet = new HashSet<>() {{
        add("chenpengfei");
    }};

    @Override
    public IPage<SelfProductListVo> pageList(SelfProductForm form) {
        IPage<SelfProductDto> page = productRepository.pageList(form);
        List<SelfProductDto> records = page.getRecords();

        if (CollectionUtil.isEmpty(records)) {
            return new Page<>(form.getCurrent(), form.getSize(), page.getTotal());
        }

        // 临时紧急需求，需要对用户名为chenpengfei的用户处理字段权限，禁止其查看商品价格
        String username = SecurityUtils.getUsername();
        boolean needShowPrice = !noPriceAuthorizationUsernameSet.contains(username);

        List<SelfProductListVo> selfProductListVoList = SelfProductListVo.convertSelfProductList(records, needShowPrice);

        //  获取虚拟sku以及其他跟虚拟sku有关的数据
        Map<String, List<VirtualSkuAndIdDp>> virtualSkuMap = virtualProductRepository.getVirtualSkuBySelfProductSku(
                selfProductListVoList.stream().map(SelfProductListVo::getSelfProductId).collect(Collectors.toList())
        );
        if (CollectionUtil.isNotEmpty(virtualSkuMap)) {
            selfProductListVoList.forEach(pro -> pro.setVirtualSkuList(virtualSkuMap.getOrDefault(pro.getSelfProductId(), null)));
        }

        // 产品大类
        Map<String, ProductCategoryDTO> productCategoryDTOMap = productCategoryService.selectCategoryLeafTree();

        // 运营、跟单人、创建人
        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        for (SelfProductListVo s : selfProductListVoList) {
            CreatorEditorDp creatorEditorDp = s.getCreatorEditorDp();
            String createBy = collect.get(creatorEditorDp.creator());
            if (StrUtil.isBlank(createBy)) {
                createBy = creatorEditorDp.creator();
            }
            s.setCreatorEditorDp(new CreatorEditorDp(createBy, creatorEditorDp.editor(), creatorEditorDp.createDate(), creatorEditorDp.editDate()));

            PurchaseInfoDp purchaseInfoDp = s.getPurchaseInfoDp();
            String buyer = collect.get(purchaseInfoDp.buyer());
            if (StrUtil.isBlank(buyer)) {
                buyer = purchaseInfoDp.buyer();
            }
            s.setPurchaseInfoDp(new PurchaseInfoDp(purchaseInfoDp.purchaseDate(), buyer, purchaseInfoDp.currency(), purchaseInfoDp.priceDp(), purchaseInfoDp.factoryCode()));

            ProductCategoryDTO parent = productCategoryDTOMap.getOrDefault(s.getCategoryId(), null);
            if (parent != null) {
                s.setCategoryName(parent.getCategoryName());
            }
        }
        IPage<SelfProductListVo> returnPage = new Page<>(form.getCurrent(), form.getSize());
        returnPage.setTotal(page.getTotal());
        returnPage.setRecords(selfProductListVoList);
        return returnPage;
    }

    @Override
    public SelfProductDetailVo getDetail(String productId) {
        SelfProductDO selfProductDO = productRepository.productDetail(productId);
        if (selfProductDO == null) {
            throw new RuntimeException("没有该商品");
        }
        FactoryInfoDO factoryInfoDO = factoryInfoRepository.getById(selfProductDO.getFactoryId());

        // 临时紧急需求，需要对用户名为chenpengfei的用户处理字段权限，禁止其查看商品价格
        String username = SecurityUtils.getUsername();
        boolean needShowPrice = true;
        if (noPriceAuthorizationUsernameSet.contains(username)) {
            selfProductDO.setCurrency(null);
            needShowPrice = false;
        }

        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));
        String buyer = collect.getOrDefault(selfProductDO.getBuyer(), selfProductDO.getBuyer());
        selfProductDO.setBuyer(buyer);
        return SelfProductDetailVo.convertProductDetail(selfProductDO, factoryInfoDO.getFactoryCode(), needShowPrice);
    }

    @Override
    @Async
    public void importProductInfo(InputStream file, byte[] byteArrayResource, String fileName) {
        try {
            productImportService.importExcelToSelfProduct(file, byteArrayResource, fileName);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Async
    public void updateSelfProductCommodityInspection(InputStream file, byte[] byteArrayResource, String fileName) {
        productImportService.updateSelfProductCommodityInspection(file, byteArrayResource, fileName);
    }

    @Override
    public List<BuyerSearchVo> getBuyerSet() {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        UserParams params = new UserParams();
        params.setPostId(UserPostEnum.PRODUCT_MANAGER.getCode());
        ResultDTO resultDTO = restTemplateUtil.post(params, ResultDTO.class, SYS_USER_INFO);
        List userList = JSON.to(List.class, resultDTO.getData());
        List<BuyerSearchVo> buyerSearchVoList = new ArrayList<>();
        userList.forEach(i -> {
            UserInteriorVO userInteriorVO = JSON.to(UserInteriorVO.class, i);
            buyerSearchVoList.add(BuyerSearchVo.builder().userName(userInteriorVO.getUserName()).nickName(userInteriorVO.getNickName()).build());
        });
        return buyerSearchVoList;
    }

    @OperationLog(content = "保存自定义产品备注", operationType = "自定义产品编辑")
    @Override
    public boolean saveSelfProductRemarks(RemarksSaveForm form, LogTrackNumDto trackNumDto) {
        RemarksSaveDp saveDp = new RemarksSaveDp(form.getId(), form.getRemarks());
        return productRepository.saveRemarks(saveDp) == 1;
    }

    @Override
    @OperationLog(content = "删除自定义产品", operationType = "自定义产品编辑")
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSelfProduct(String selfProductId, LogTrackNumDto dto) {
        // 删除虚拟商品
        SelfProductDO selfProductDO = productRepository.productDetail(selfProductId);
        if (selfProductDO == null) {
            throw new RuntimeException("自定义sku为空，请重新加载页面");
        }
        // 获取到待删除的虚拟sku下的spu集合，之后需要将没有关联虚拟sku的spu删除掉
        List<VirtualProductDO> allVirtualProduct = virtualProductRepository.getAllVirtualProduct(Collections.singletonList(selfProductDO.getId()));
        Set<String> spuIdSet = allVirtualProduct.stream().map(VirtualProductDO::getSpuId).collect(Collectors.toSet());
        // 校验库存是否存在虚拟sku
        List<String> virtualIds = allVirtualProduct.stream().map(VirtualProductDO::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(virtualIds)) {
            RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
            InventoryInfoQuery inventoryInfoQuery = new InventoryInfoQuery();
            inventoryInfoQuery.setVirtualSkuIdList(virtualIds);
            ResultDTO resultDTO = restTemplateUtil.post(inventoryInfoQuery, ResultDTO.class, INVENTORY_SYSTEM_HAS_REDUNDANCY_VIRTUAL_SKU_ID);
            Boolean hasRedundancy = JSON.to(Boolean.class, resultDTO.getData());
            if (hasRedundancy) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "先在库存管理删除该产品库存后再删除产品！");
            }
        }
        List<VirtualProductDO> virtualProductDOList = virtualProductRepository.list(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getSelfProductSkuId, selfProductDO.getId()));
        // 删除升级关系
        if (CollectionUtil.isNotEmpty(virtualProductDOList)) {
            virtualProductDOList.forEach(virtualProductDO -> {
                if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())) {
                    upgradeProductService.deleteUpgradeRelation(virtualProductDO.getUpgradeId());
                }
            });
        }

        int deleteVirtualProduct = virtualProductRepository.deleteVirtualProductBySku(selfProductDO.getId());

        // 只要正常返回即可删除自定义产品，因为该产品下面有可能没有虚拟sku
        if (deleteVirtualProduct >= 0) {
            // 删除spu相关信息
            for (String spuId : spuIdSet) {
                List<VirtualProductDO> virtualSkuList = virtualProductRepository.getVirtualProductListUnderSpu(
                        VirtualSkuInfoUnderSpuForm.builder().spu(spuId).build()
                );
                if (CollectionUtil.isEmpty(virtualSkuList)) {
                    spuProductRepository.deleteSpuProduct(spuId);
                }
            }
            // 删除自定义产品
            int deleteSelfProduct = productRepository.deleteSelfProduct(selfProductId);
            // 同步需要删除目标日销的数据
            CalculationForm form = CalculationForm.builder().virtualSkuIdList(virtualIds).build();
            RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
            ResultDTO<Boolean> deleteTargetSalesResult = restTemplateUtils.post(form, ResultDTO.class, DELETE_TARGET_SALES);
            Boolean delete = JSON.to(Boolean.class, deleteTargetSalesResult.getData());
            if (!delete) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "先在库存管理删除该产品库存后再删除产品");
            }
            return deleteSelfProduct > 0;
        }
        return false;
    }

    @Override
    @OperationLog(content = "删除自定义产品", operationType = "自定义产品编辑")
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSelfProductBatch(List<String> selfProductIds, LogTrackNumMapDto dto) {
        // 删除虚拟商品
        List<SelfProductDO> selfProductDO = productRepository.listByIds(selfProductIds);
        if (CollectionUtil.isEmpty(selfProductDO)) {
            throw new RuntimeException("自定义sku为空，请重新加载页面");
        }
        List<String> selfProductIdList = selfProductDO.stream().map(SelfProductDO::getId).collect(Collectors.toList());
        // 获取到待删除的虚拟sku下的spu集合，之后需要将没有关联虚拟sku的spu删除掉
        List<VirtualProductDO> allVirtualProduct = virtualProductRepository.getAllVirtualProduct(selfProductIdList);
        Set<String> spuIdSet = allVirtualProduct.stream().map(VirtualProductDO::getSpuId).collect(Collectors.toSet());
        // 校验库存是否存在虚拟sku
        List<String> virtualIds = allVirtualProduct.stream().map(VirtualProductDO::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(virtualIds)) {
            RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
            InventoryInfoQuery inventoryInfoQuery = new InventoryInfoQuery();
            inventoryInfoQuery.setVirtualSkuIdList(virtualIds);
            ResultDTO resultDTO = restTemplateUtil.post(inventoryInfoQuery, ResultDTO.class, INVENTORY_SYSTEM_HAS_REDUNDANCY_VIRTUAL_SKU_ID_LIST);
            List<String> hasRedundancyList = JSON.to(List.class, resultDTO.getData());
            if (CollectionUtil.isNotEmpty(hasRedundancyList)) {
                List<VirtualProductDO> virtualProductDOList = virtualProductRepository.listByIds(hasRedundancyList);
                if(CollectionUtil.isNotEmpty(virtualProductDOList) && virtualProductDOList.size() > 3) {
                    // 删除第三个之后的数据
                    virtualProductDOList = virtualProductDOList.subList(0, 3);
                }
                String virtualSkuStr = virtualProductDOList.stream().map(VirtualProductDO::getVirtualSku).collect(Collectors.joining(","));
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "先在库存管理删除该产品库存后再删除产品："+virtualSkuStr);
            }
        }

        List<VirtualProductDO> upgradeList = allVirtualProduct.stream().filter(i -> StrUtil.isNotBlank(i.getUpgradeId())).collect(Collectors.toList());
        // 删除升级款
        if (CollectionUtil.isNotEmpty(upgradeList)) {
            upgradeProductService.deleteUpgradeRelationList(upgradeList.stream().map(VirtualProductDO::getUpgradeId).collect(Collectors.toList()));
        }

        boolean deleteVirtualProduct = virtualProductRepository.deleteVirtualProductByVirtualIds(virtualIds);

        // 只要正常返回即可删除自定义产品，因为该产品下面有可能没有虚拟sku
        if (deleteVirtualProduct) {
            Map<String, String> logMap = new HashMap<>();
            selfProductIdList.forEach(selfProductId -> {
                logMap.put(selfProductId, "删除自定义产品");
            });
            dto.setLogMap(logMap);
            dto.setAuthorization(SecurityUtils.getToken());
            // 删除spu相关信息
            for (String spuId : spuIdSet) {
                List<VirtualProductDO> virtualSkuList = virtualProductRepository.getVirtualProductListUnderSpu(
                        VirtualSkuInfoUnderSpuForm.builder().spu(spuId).build()
                );
                if (CollectionUtil.isEmpty(virtualSkuList)) {
                    spuProductRepository.deleteSpuProduct(spuId);
                }
            }
            // 删除自定义产品
             productRepository.removeBatchByIds(selfProductIdList);
            // 同步需要删除目标日销的数据
            CalculationForm form = CalculationForm.builder().virtualSkuIdList(virtualIds).build();
            RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
            ResultDTO<Boolean> deleteTargetSalesResult = restTemplateUtils.post(form, ResultDTO.class, DELETE_TARGET_SALES);
            Boolean delete = JSON.to(Boolean.class, deleteTargetSalesResult.getData());
            if (!delete) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "先在库存管理删除该产品库存后再删除产品");
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SelfProductUpdateVo updateSelfProduct(SelfProductUpdateForm form, String name) {
        //校验
        if (!CollectionUtil.isEmpty(form.getNewVirtualSkuList())) {
            SelfProductUpdateVo selfProductUpdateVo = updateBeforeCheck(form);
            if (!selfProductUpdateVo.getResultStatus()) {
                return selfProductUpdateVo;
            }
        }
        Date now = new Date();
        //自定义sku是否修改
        boolean skuChange = false;
        List<String> skuIdList = new ArrayList<>();
        //更新自定义sku
        if (StrUtil.isNotBlank(form.getNewSku())) {
            SelfProductDO selfProductDO = productRepository.getOne(Wrappers.lambdaQuery(SelfProductDO.class)
                    .eq(SelfProductDO::getSku, form.getNewSku()));
            SelfProductDO oldSelfProductDO = productRepository.getById(form.getSelfProductId());
            oldSelfProductDO.setUpdateBy(name);
            oldSelfProductDO.setUpdateDate(now);
            if (selfProductDO != null && !form.getSelfProductId().equals(selfProductDO.getId())) {
                oldSelfProductDO.setId(selfProductDO.getId());
                oldSelfProductDO.setSku(form.getNewSku());
                oldSelfProductDO.setCreateBy(selfProductDO.getCreateBy());
                oldSelfProductDO.setCreateDate(selfProductDO.getCreateDate());
                skuChange = productRepository.updateById(oldSelfProductDO);
                productRepository.update(new SelfProductDO(), new UpdateWrapper<SelfProductDO>().lambda()
                        .set(SelfProductDO::getStatus, StatusEnum.DELETED.getCode())
                        .set(SelfProductDO::getUpdateBy, name)
                        .set(SelfProductDO::getUpdateDate, now)
                        .set(SelfProductDO::getPriceWithTaxes, selfProductDO.getPriceWithTaxes())
                        .set(SelfProductDO::getRemarks, selfProductDO.getRemarks())
                        .eq(SelfProductDO::getId, form.getSelfProductId())
                );
                List<VirtualProductDO> virtualProductDOS = virtualProductRepository.list(Wrappers.<VirtualProductDO>lambdaQuery()
                        .eq(VirtualProductDO::getSelfProductSkuId, form.getSelfProductId()));

                virtualProductRepository.updateBatchById(virtualProductDOS.stream().map(virtualSku -> VirtualProductDO.builder()
                        .id(virtualSku.getId())
                        .selfProductSkuId(selfProductDO.getId())
                        .oldSku(virtualSku.getOldSku())
                        .spuId(virtualSku.getSpuId())
                        .remarks(virtualSku.getRemarks())
                        .updateBy(name)
                        .updateDate(now).build()).collect(Collectors.toList()));
                skuIdList = virtualProductRepository.list(Wrappers.<VirtualProductDO>lambdaQuery()
                        .eq(VirtualProductDO::getSelfProductSkuId, selfProductDO.getId())).stream().map(VirtualProductDO::getId).toList();
            } else if (selfProductDO == null) {
                oldSelfProductDO.setSku(form.getNewSku());
                skuChange = productRepository.updateById(oldSelfProductDO);
                List<VirtualProductDO> VirtualList = virtualProductRepository.list(Wrappers.<VirtualProductDO>lambdaQuery()
                        .eq(VirtualProductDO::getSelfProductSkuId, form.getSelfProductId()));
                skuIdList = VirtualList.stream().map(VirtualProductDO::getId).toList();
            }
        }
        //更新虚拟sku
        if (!CollectionUtil.isEmpty(form.getNewVirtualSkuList())) {
            List<String> newVirtualSkuList = form.getNewVirtualSkuList().stream().map(VirtualSkuProductUpdateDto::getVirtualSkuId).toList();
            if (!skuChange) {
                skuIdList = newVirtualSkuList;
            }

            List<VirtualProductDO> virtualProductDOS = virtualProductRepository.listByIds(newVirtualSkuList);

            Map<String, VirtualProductDO> virtualMap = virtualProductDOS.stream().collect(Collectors.toMap(VirtualProductDO::getId, virtualProductDO -> virtualProductDO));

            virtualProductRepository.updateBatchById(form.getNewVirtualSkuList().stream().map(newVirtualSku -> VirtualProductDO.builder()
                    .id(newVirtualSku.getVirtualSkuId())
                    .virtualSku(newVirtualSku.getNewSku())
                    .oldSku(virtualMap.get(newVirtualSku.getVirtualSkuId()).getVirtualSku())
                    .spuId(virtualMap.get(newVirtualSku.getVirtualSkuId()).getSpuId())
                    .remarks(virtualMap.get(newVirtualSku.getVirtualSkuId()).getRemarks())
                    .updateBy(name)
                    .updateDate(now).build()).collect(Collectors.toList()));
        }
        //保存快照
        productSnapshotService.saveProductSnapshotList(skuIdList);

        return SelfProductUpdateVo.builder().resultStatus(true).build();
    }

    @Override
    public SelfProductUpdateVo updateBeforeCheck(SelfProductUpdateForm form) {
        if (!CollectionUtil.isEmpty(form.getNewVirtualSkuList())) {
            List<String> skuList = form.getNewVirtualSkuList().stream().map(VirtualSkuProductUpdateDto::getNewSku).collect(Collectors.toList());
            //校验
            SelfProductUpdateVo result = new UpdateVirtualSkuDp(skuList, null).result();
            if (result != null) {
                return result;
            }
            // 校验虚拟sku是否与其他虚拟sku，老sku重复
            List<VirtualProductDO> checkVirtualList = virtualProductRepository.list(Wrappers.lambdaQuery(VirtualProductDO.class)
                    .in(VirtualProductDO::getVirtualSku, skuList)
                    .or()
                    .in(VirtualProductDO::getOldSku, skuList)
            );
            List<VirtualSkuProductUpdateDto> virtualSkuProductUpdateDtoList = new ArrayList<>();

            if (CollectionUtil.isNotEmpty(checkVirtualList)) {
                //对比form.getNewVirtualSkuList()中的NewSku与checkVirtualList中的VirtualSku或者oldSku，如果重复，记录下来
                //List<String> virSku = form.getNewVirtualSkuList().stream().map(VirtualSkuProductUpdateDto::getNewSku).toList();
                checkVirtualList.forEach(virtualProductDO -> {
                    VirtualSkuProductUpdateDto dto = new VirtualSkuProductUpdateDto();
                    if (skuList.contains(virtualProductDO.getVirtualSku())) {
                        dto.setVirtualSku(virtualProductDO.getVirtualSku());
                        dto.setMessage("虚拟SKU与系统中虚拟SKU重复");
                        virtualSkuProductUpdateDtoList.add(dto);
                    }
                    if (skuList.contains(virtualProductDO.getOldSku())) {
                        dto.setVirtualSku(virtualProductDO.getOldSku());
                        dto.setMessage("虚拟SKU与系统中老SKU重复");
                        virtualSkuProductUpdateDtoList.add(dto);
                    }

                });
                if (CollectionUtil.isNotEmpty(virtualSkuProductUpdateDtoList)) {
                    return SelfProductUpdateVo.builder().resultStatus(false).virtualSkuList(virtualSkuProductUpdateDtoList).build();
                }
            }
        }
        String message = null;
        if (StrUtil.isNotBlank(form.getNewSku())) {
            SelfProductDO selfProductDO = productRepository.getOne(Wrappers.lambdaQuery(SelfProductDO.class)
                    .eq(SelfProductDO::getSku, form.getNewSku()));
            if (selfProductDO != null && !form.getSelfProductId().equals(selfProductDO.getId())) {
                message = "merge";
            }
        }
        return SelfProductUpdateVo.builder().resultStatus(true).message(message).build();
    }

    @Override
    public List<SelfAndVirtualVo> virtualList(String selfProductId) {
        List<VirtualProductDO> list = virtualProductRepository.list(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getSelfProductSkuId, selfProductId));
        List<SelfAndVirtualVo> selfAndVirtualVos = SelfAndVirtualVo.convertVirProductList(list);

        SelfProductDO selfById = productRepository.getById(selfProductId);
        if (selfById != null) {
            selfAndVirtualVos.add(SelfAndVirtualVo.builder().id(selfProductId).sku(selfById.getSku()).type("0").build());
        }
        return selfAndVirtualVos;
    }

    @Override
    public void exportInfo(SelfProductForm form, HttpServletResponse response) {
        List<SelfProductInfoExportExcel> exportInfo = productRepository.getExportInfo(form);
        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        // 临时紧急需求，需要对用户名为chenpengfei的用户处理字段权限，禁止其查看商品价格
        String username = SecurityUtils.getUsername();

        Map<String, String> channelIdNameMap = channelService.getChannelIdNameMap();

        Map<String, ProductCategoryDTO> productCategoryDTOMap = productCategoryService.selectCategoryLeafTree();
        for (var excel : exportInfo) {
            if (noPriceAuthorizationUsernameSet.contains(username)) {
                excel.setPrice(null);
                excel.setTaxes(null);
                excel.setPriceWithTaxes(null);
                excel.setCurrency(null);
            }

            if (StrUtil.isNotBlank(excel.getChannel())) {
                excel.setChannel(channelIdNameMap.getOrDefault(excel.getChannel(), excel.getChannel()));
            }
            String taxes = excel.getTaxes();
            if (StrUtil.isNotBlank(taxes)) {
                IsTexesEnum isTexesEnum = IsTexesEnum.ofCode(taxes);
                excel.setTaxes(isTexesEnum != null ? isTexesEnum.getDesc() : taxes);
            }
            String productStatus = excel.getProductStatus();
            if (StrUtil.isNotBlank(productStatus)) {
                VirtualProductStatusEnum statusEnum = VirtualProductStatusEnum.ofCode(Integer.valueOf(productStatus));
                excel.setProductStatus(statusEnum != null ? statusEnum.getDesc() : productStatus);
            }
            String productType = excel.getProductType();
            if (StrUtil.isNotBlank(productType)) {
                VirtualProductTypeEnum typeEnum = VirtualProductTypeEnum.ofCode(Integer.valueOf(productType));
                excel.setProductType(typeEnum != null ? typeEnum.getDesc() : productType);
            }
            String subType = excel.getSubType();
            if (StrUtil.isNotBlank(subType)) {
                VirtualSubTypeEnum subTypeEnum = VirtualSubTypeEnum.ofCode(Integer.valueOf(subType));
                excel.setSubType(subTypeEnum != null ? subTypeEnum.getDesc() : subType);
            }
            String operator = excel.getOperator();
            if (StrUtil.isNotBlank(operator)) {
                String operatorNames = Arrays.stream(operator.split(",", 0))
                        .map(op -> StrUtil.isNotBlank(collect.get(op)) ? collect.get(op) : op)
                        .collect(Collectors.joining(","));
                excel.setOperator(operatorNames);
            }
            String buyer = excel.getBuyer();
            if (StrUtil.isNotBlank(buyer)) {
                excel.setBuyer(collect.getOrDefault(buyer, buyer));
            }
            ProductCategoryDTO parent = productCategoryDTOMap.getOrDefault(excel.getCategoryId(), null);
            if (parent != null) {
                excel.setCategory(parent.getCategoryName());
            }
            if (StrUtil.isNotBlank(excel.getBorrowingStrategy())) {
                BorrowingStrategyEnum borrowingStrategyEnum = BorrowingStrategyEnum.ofCode(Integer.valueOf(excel.getBorrowingStrategy()));
                excel.setBorrowingStrategy(borrowingStrategyEnum != null ? borrowingStrategyEnum.getDesc() : null);
            }

            YesOrNoEnum.ofCode(excel.getCommodityInspection()).ifPresent(orNoEnum -> excel.setCommodityInspection(orNoEnum.getDesc()));
        }
        ExcelWriter excelWriter = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("自定义产品导出", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            WriteSheet writeSheet = EasyExcel.writerSheet(1, "自定义产品").head(SelfProductInfoExportExcel.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();

            excelWriter.write(exportInfo, writeSheet);
        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        log.info("导出自定义产品:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

    @Override
    public List<ProductCategoryDTO> category() {
        return productCategoryService.selectCategoryTree();
    }
}
