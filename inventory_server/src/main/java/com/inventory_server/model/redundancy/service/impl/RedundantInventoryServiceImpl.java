package com.inventory_server.model.redundancy.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.dp.ProduceDaysDp;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.UserPostEnum;
import com.crafts_mirror.utils.enums.UserStatusEnum;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.crafts_mirror.utils.utils.DateUtils;
import com.crafts_mirror.utils.utils.IOUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.utils.SnowflakeIdWorker;
import com.inventory_server.applications.cqe.InventoryInfoQuery;
import com.inventory_server.applications.cqe.LeadTimeCommand;
import com.inventory_server.applications.dto.FactoryFinishedInventoryDto;
import com.inventory_server.applications.dto.*;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.infrastructures.assembler.RedundantInventoryAssembler;
import com.inventory_server.infrastructures.entity.form.FileMissionForm;
import com.inventory_server.infrastructures.exception.BusinessException;
import com.inventory_server.infrastructures.handler.ExcelWidthStyleStrategy;
import com.inventory_server.infrastructures.utils.MockResultUtils;
import com.inventory_server.model.channel.service.IChannelInfoService;
import com.inventory_server.model.file.service.IFileCenterService;
import com.inventory_server.model.product.bo.SelfAndVirtualBO;
import com.inventory_server.model.product.entity.dos.SelfProductDO;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.product.entity.dto.ProductSnapshotDto;
import com.inventory_server.model.product.entity.form.UserParams;
import com.inventory_server.model.product.entity.vo.OperatorSearchVo;
import com.inventory_server.model.product.entity.vo.UserInteriorVO;
import com.inventory_server.model.product.enums.BorrowingStrategyEnum;
import com.inventory_server.model.product.enums.UpgradeTypeEnum;
import com.inventory_server.model.product.enums.VirtualProductTypeEnum;
import com.inventory_server.model.product.enums.VirtualSubTypeEnum;
import com.inventory_server.model.product.repository.databaseRepository.SelfProductRepositoryImpl;
import com.inventory_server.model.product.repository.databaseRepository.VirtualProductRepositoryImpl;
import com.inventory_server.model.product.repository.interiorRepository.ISnapshotRepository;
import com.inventory_server.model.product.service.ISelfProductService;
import com.inventory_server.model.redundancy.entity.aggregate.InventoryFactoryInfoA;
import com.inventory_server.model.redundancy.entity.aggregate.InventoryFactoryPlanInfoA;
import com.inventory_server.model.redundancy.entity.bo.InventoryForeignRedundantBO;
import com.inventory_server.model.redundancy.entity.bo.InventoryForeignStoreBO;
import com.inventory_server.model.redundancy.entity.bo.InventoryOnShippingRedundantBO;
import com.inventory_server.model.redundancy.entity.bo.InventoryRulesBO;
import com.inventory_server.model.redundancy.entity.dos.*;
import com.inventory_server.model.redundancy.entity.dp.*;
import com.inventory_server.model.redundancy.entity.dto.*;
import com.inventory_server.model.redundancy.entity.excelObj.*;
import com.inventory_server.model.redundancy.entity.form.DeliveryCalculationForm;
import com.inventory_server.model.redundancy.entity.form.NormalDeliveryInventoryForm;
import com.inventory_server.model.redundancy.entity.form.UrgentHeadShipDateForm;
import com.inventory_server.model.redundancy.entity.vo.DeliveryCalculationVo;
import com.inventory_server.model.redundancy.entity.vo.NormalDeliveryWatchBoardVo;
import com.inventory_server.model.redundancy.entity.vo.TrialWatchBoardVo;
import com.inventory_server.model.redundancy.enums.FactoryPlanInfoEnum;
import com.inventory_server.model.redundancy.enums.UrgentCircleEditedEnum;
import com.inventory_server.model.redundancy.enums.redundantImportSheetNameEnum;
import com.inventory_server.model.redundancy.listener.redundant.*;
import com.inventory_server.model.redundancy.listener.template.ExcelTemplateGenerator;
import com.inventory_server.model.redundancy.repository.dataRepository.RedundantInventoryRepository;
import com.inventory_server.model.redundancy.repository.interiorRepository.IDeliveryInventoryRepository;
import com.inventory_server.model.redundancy.repository.redisRepository.IChannelMappingRedisRepository;
import com.inventory_server.model.redundancy.service.*;
import com.inventory_server.model.system.service.ISysUserInteriorService;
import com.inventory_server.model.targetSales.service.ITargetSalesService;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.service.IWarehouseService;
import com.inventory_server.model.warning.unsalable.entity.form.UnsalableInventoryCalForm;
import com.inventory_server.model.warning.unsalable.service.IUnsalableInventorySaveService;
import com.inventory_server.model.warning.urgentPurchase.entity.form.UrgentPurchaseMQForm;
import com.inventory_server.model.warning.urgentPurchase.service.IUrgentPurchaseSaveService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.crafts_mirror.utils.constant.Constants.CHANNEL_MAPPING;
import static com.crafts_mirror.utils.constant.DateFormatConstant.*;
import static com.crafts_mirror.utils.constant.MQConstants.UNSALABLE_INVENTORY_WARNING_TOPIC;
import static com.crafts_mirror.utils.constant.MQConstants.URGENT_PURCHASE_TOPIC;
import static com.crafts_mirror.utils.constant.SystemConstant.*;
import static com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum.*;
import static com.crafts_mirror.utils.utils.DateUtils.YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT;
import static com.inventory_server.infrastructures.consts.FilePathConstant.REDUNDANT_INVENTORY_FILE_PATH;
import static com.inventory_server.infrastructures.consts.InventoryServiceConstant.FULL_LINK_SOLD_OUT_DAYS;
import static com.inventory_server.infrastructures.consts.InventoryServiceConstant.SOLD_OUT_DAYS;
import static com.inventory_server.model.redundancy.enums.DeliveryTypeEnum.URGENT;
import static com.inventory_server.model.redundancy.enums.FactoryPlanInfoEnum.IN_PRODUCTION;
import static com.inventory_server.model.redundancy.enums.FactoryPlanInfoEnum.PRODUCT;
import static com.inventory_server.model.redundancy.enums.redundantImportSheetNameEnum.*;
import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 冗余库存信息获取、初步处理、数据储存、展示的service层
 * <AUTHOR>
 * @Date 2024/5/7 16:38
 **/
@Service
@Slf4j
public class RedundantInventoryServiceImpl implements IRedundantInventoryService {
    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private IFileCenterService IFileCenterService;

    @Resource
    private SnowflakeIdWorker snowflakeIdWorker;

    @Resource
    private ThreadPoolTaskExecutor importExcelThreadPool;

    @Resource
    private RedundantInventoryRepository redundantInventoryRepository;

    @Resource
    private IInventoryFactoryPlanInfoService inventoryFactoryPlanInfoService;

    @Resource
    private IInventoryForeignStoreService inventoryForeignStoreService;

    @Resource
    private IInventorySaleRulesService inventorySaleRulesService;

    @Resource
    private IInventoryWatchBoardService inventoryWatchBoardService;

    @Resource
    private IInventoryMockTableService inventoryMockTableService;

    @Resource
    private IInventoryFactoryRedundantInfoService inventoryFactoryRedundantInfoService;

    @Resource
    private IInventoryForeignAndShippingRedundantInfoService inventoryForeignRedundantInfoService;

    @Resource
    private ISoldOutDaysService soldOutDaysService;

    @Resource
    private ISelfProductService selfProductService;

    @Resource
    private RedundantInventoryAssembler redundantInventoryAssembler;

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource
    private ISnapshotRepository snapshotRepository;

    @Resource
    private IWarehouseService warehouseService;

    @Resource
    private ISysUserInteriorService sysUserInteriorService;

    @Resource
    private IDeliveryInventoryRepository deliveryInventoryRepository;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private IRedundancySaveService redundancySaveService;

    @Resource(name = "normalRedundancyEndCalServiceImpl")
    private IRedundancyEndCalService redundancyEndCalService;

    private final DateTimeFormatter formatter = YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT;
    private final DateTimeFormatter slashYearMonthFormatter = DateTimeFormatter.ofPattern(YYYY_MM_DATE_FORMAT_SLASH);
    private final DateTimeFormatter hyphenFormatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_HYPHEN);
    private final DateTimeFormatter chineseFormatter = DateTimeFormatter.ofPattern(YYYY_M_DATE_FORMAT_CHINESE);

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepositoryImpl;

    @Resource
    private IChannelMappingRedisRepository channelRedisRepository;

    @Resource
    private SelfProductRepositoryImpl selfProductRepositoryImpl;

    @Resource
    private IUnsalableInventorySaveService unsalableInventorySaveService;

    @Resource
    private IUrgentPurchaseSaveService urgentPurchaseSaveService;

    @Resource
    private ITargetSalesService targetSalesService;

    @Resource
    private IChannelInfoService channelInfoService;

    @Resource
    private InterventionalTimeService interventionalTimeService;

    private final byte[] lock = new byte[0];

    @Override
    public ImportResultDto importRedundantInventoryExcel(InputStream file, byte[] fileBytes, String fileName) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        String snowId = String.valueOf(snowflakeIdWorker.nextId());
        String key = snowId + "." + Objects.requireNonNull(fileName).substring(fileName.lastIndexOf(".") + 1);
        MultiValueMap<String, Object> httpEntity = IFileCenterService.putFile(fileBytes, fileName, REDUNDANT_INVENTORY_FILE_PATH + key, DateUtil.offsetDay(new Date(), 730).toString());
        IFileCenterService.uploadFile(httpEntity, restTemplateUtil);

        ResultDTO<String> restResult = restTemplateUtil.post(
                FileMissionForm.builder().fileName(fileName)
                        .importStatus("正在导入")
                        .type("冗余库存计划导入")
                        .filePath(key)
                        .build(),
                ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
        );

        String missionId = restResult.getData();
        List<String> errorList = new CopyOnWriteArrayList<>();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            file.transferTo(baos);
            // 并行解析各sheet页数据
            ByteArrayInputStream rulesInputStream = new ByteArrayInputStream(baos.toByteArray());
            CompletableFuture<ImportInventoryRulesDto> rulesFuture = getRulesFuture(rulesInputStream, fileName, errorList);

            ByteArrayInputStream targetSalesStream = new ByteArrayInputStream(baos.toByteArray());
            CompletableFuture<ImportTargetSalesDto> targetSalesFuture = getTargetSalesFuture(targetSalesStream, fileName, errorList);

            ByteArrayInputStream factoryPlanStream = new ByteArrayInputStream(baos.toByteArray());
            CompletableFuture<Map<String, FactoryPlanDp>> factoryPlanFuture = getFactoryPlanFuture(factoryPlanStream, fileName, errorList);

            ByteArrayInputStream stockQuantityStream = new ByteArrayInputStream(baos.toByteArray());
            CompletableFuture<Map<String, StockQuantityDp>> stockQuantityFuture = getStockQuantityFuture(stockQuantityStream, fileName, errorList);

            DateTime now = DateTime.now();
            DateTime dateTime = DateUtil.beginOfDay(now);
            ByteArrayInputStream amStockQuantityStream = new ByteArrayInputStream(baos.toByteArray());
            CompletableFuture<Map<String, AmStockQuantityDp>> amStockQuantityFuture = getAmStockQuantityFuture(amStockQuantityStream, fileName, errorList, dateTime);

            List<InventoryCalDp> inventoryCalDpList = new ArrayList<>();
            try {
                inventoryCalDpList = CompletableFuture.allOf(rulesFuture, targetSalesFuture, factoryPlanFuture, stockQuantityFuture, amStockQuantityFuture)
                        .thenApply(v -> {
                            // 获取各sheet页结果，联合校验后组装成算法需要的数据
                            ImportInventoryRulesDto inventoryRulesDto = rulesFuture.join();
                            ImportTargetSalesDto importTargetSalesDto = targetSalesFuture.join();
                            Map<String, FactoryPlanDp> factoryPlanDpMap = factoryPlanFuture.join();
                            Map<String, StockQuantityDp> stockQuantityDpMap = stockQuantityFuture.join();
                            Map<String, AmStockQuantityDp> amStockQuantityDpMap = amStockQuantityFuture.join();
                            return getInventoryCalDpList(inventoryRulesDto, importTargetSalesDto, factoryPlanDpMap,
                                    stockQuantityDpMap, amStockQuantityDpMap);
                        }).get(10, TimeUnit.HOURS);
            } catch (InterruptedException | ExecutionException e) {
                log.error("冗余库存导入异常：", e);
                errorList.add(String.format("冗余库存导入异常，异常原因：%s，请联系研发人员处理", e.getCause().getMessage()));
            } catch (TimeoutException e) {
                log.error("冗余库存导入超时：", e);
                errorList.add("冗余库存导入超时，请联系研发人员处理");
            } finally {
                IOUtils.closeIOStream(baos);
            }

            if (errorList.isEmpty()) {
                restTemplateUtil.post(FileMissionForm.builder()
                        .importStatus("导入分析中")
                        .missionId(missionId)
                        .build(), ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
            } else {
                restTemplateUtil.post(FileMissionForm.builder()
                        .importStatus("失败")
                        .importResult(String.format("失败：%s", errorList.size()))
                        .failedResultList(errorList)
                        .missionId(missionId)
                        .build(), ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
                throw new RuntimeException("导入失败");
            }
            return new ImportResultDto(missionId, inventoryCalDpList);
        } catch (IOException e) {
            log.error("冗余库存导入异常：", e);
            errorList.add(String.format("冗余库存导入异常，异常原因：%s，请联系研发人员处理", e.getMessage()));
            throw new RuntimeException("冗余库存导入异常：", e);
        } finally {
            IOUtils.closeIOStream(baos);
        }
    }

    @Override
    public void exportTemplate(HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("库存导入模板", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
            ExcelTemplateGenerator excelTemplateGenerator = new ExcelTemplateGenerator();
            excelTemplateGenerator.generateTemplate(excelWriter, senboWarehouseList);

        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    @Override
    public DeliveryCalculationVo getNormalShippingCalculationResult(List<InventoryCalDp> inventoryCalDps, int solidDate) {
        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        Map<String, Integer> headShippingDaysMap = senboWarehouseList.stream()
                .collect(Collectors.toMap(s -> s.getSenboWarehouseId().toString(), SenboWarehouseDto::getHeadShippingDate));
        DeliveryCalculationForm form = convertToRequestForm(inventoryCalDps, DateTime.now(), true,
                true, true, headShippingDaysMap, solidDate);

        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO result = restTemplateUtil.post(form, ResultDTO.class, DELIVERY_NORMAL_CALCULATION_URL);
        if (!Objects.equals(result.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("冗余库存计算时获取发货数据失败，异常原因：{}", result.getMessage());
            throw new RuntimeException("冗余库存计算时获取发货数据失败，异常原因：" + result.getMessage());
        }
        DeliveryCalculationVo deliveryCalculationVo = JSON.to(DeliveryCalculationVo.class, result.getData());
        supplementEmptyMockTable(deliveryCalculationVo, form.getDeliveryCalRequestDtoList(), LocalDate.now());
        return deliveryCalculationVo;
    }

    @Override
    public DeliveryCalculationVo getShippingCalculationResultWithRequirement(List<InventoryCalDp> inventoryCalDps,
                                                                             boolean needFactoryPlan, boolean needOnShippingInventory,
                                                                             int solidDate, DateTime dateTime) {
        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        Map<String, Integer> headShippingDaysMap = senboWarehouseList.stream()
                .collect(Collectors.toMap(s -> s.getSenboWarehouseId().toString(), SenboWarehouseDto::getHeadShippingDate));
        DeliveryCalculationForm form = convertToRequestForm(inventoryCalDps, dateTime, needFactoryPlan,
                needOnShippingInventory, false, headShippingDaysMap, solidDate);

        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO result = restTemplateUtil.post(form, ResultDTO.class, DELIVERY_CALCULATION_URL);
        if (!Objects.equals(result.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("冗余库存计算时获取发货数据失败，异常原因：{}", result.getMessage());
            throw new RuntimeException("冗余库存计算时获取发货数据失败，异常原因：" + result.getMessage());
        }
        DeliveryCalculationVo deliveryCalculationVo = JSON.to(DeliveryCalculationVo.class, result.getData());
        supplementEmptyMockTable(deliveryCalculationVo, form.getDeliveryCalRequestDtoList(), LocalDate.now());
        return deliveryCalculationVo;
    }

    @Override
    public DeliveryCalculationVo getShippingCalculation(LocalDate calFinishedDate, List<InventoryCalDp> inventoryCalDps,
                                                        boolean needFactoryPlan, boolean needOnShippingInventory, int solidDate) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        Map<String, Integer> headShippingDaysMap = senboWarehouseList.stream()
                .collect(Collectors.toMap(s -> s.getSenboWarehouseId().toString(), SenboWarehouseDto::getHeadShippingDate));
        DeliveryCalculationForm form = convertToRequestForm(inventoryCalDps, DateUtils.convertToDateTime(calFinishedDate),
                needFactoryPlan, needOnShippingInventory, false, headShippingDaysMap, solidDate);
        String formattedDate = calFinishedDate.format(YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT);
        form.setCalculatedDateStr(formattedDate);
        ResultDTO result = restTemplateUtil.post(form, ResultDTO.class, DELIVERY_CALCULATE_MOCK_TABLE_URL);
        if (!Objects.equals(result.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("冗余库存计算时获取模拟试算表格异常，异常原因：{}", result.getMessage());
            throw new RuntimeException("冗余库存计算时获取模拟试算表格异常，异常原因：" + result.getMessage());
        }
        return JSON.to(DeliveryCalculationVo.class, result.getData());
    }

    @Override
    @Async("inventoryCalThreadPool")
    public void saveRedundancyInfo(String importFileId, List<InventoryCalDp> inventoryCalDpList, DeliveryCalculationVo shippingResult,
                                   RedundancyResultDto redundancyResultDto, Map<String, LocalDate> soldOutDateMap,
                                   Map<String, List<SoldOutDateBeforeTheoryDto>> soldOutDetailMap, Map<String, LocalDate> fullLinkSoldOutDateMap,
                                   Map<String, List<SoldOutDateBeforeTheoryDto>> fullLinkSoldOutDetailMap, DeliveryCalculationVo normalDeliveryResult,
                                   Map<String, Integer> headShippingDateMap, int solidDate, Map<String, SalableDateDto> salableDateMap,
                                   Map<String, SalableDateDto> fullLinkSalableDateMap, Map<String, AchievementRateDto> achievementRate) {
        var virtualImportCalMap = inventoryCalDpList.stream().collect(Collectors.toMap(k -> k.virtualProduct().getVirtualSku(), v -> v));
        var deliveryResult = shippingResult.getDeliveryCalResultList().stream().collect(Collectors.toMap(DeliveryCalResultDto::getVirtualSku, v -> v));
        var foreignRedundancyMap = redundancyResultDto.getForeignRedundancyDpList().stream()
                .collect(Collectors.toMap(RedundancyDp::getVirtualSku, v -> v));
        var factoryRedundancyMap = redundancyResultDto.getFactoryRedundancyDpList().stream()
                .collect(Collectors.toMap(RedundancyDp::getVirtualSku, v -> v));
        var onShippingRedundancyMap = redundancyResultDto.getOnShippingRedundancyDpList().stream()
                .collect(Collectors.toMap(RedundancyDp::getVirtualSku, v -> v));
        var narmalDeliveryMap = normalDeliveryResult.getDeliveryCalResultList().stream()
                .collect(Collectors.toMap(DeliveryCalResultDto::getVirtualSku, v -> v));

        // 保存正常发货流程下的发货数据
        List<NormalDeliveryInventorySaveDto> deliveryInventoryList = new ArrayList<>(virtualImportCalMap.size());

        List<UnsalableInventoryCalForm> unsalableInventoryCalFormList = new ArrayList<>();
        List<UrgentPurchaseMQForm> urgentPurchaseMQFormList = new ArrayList<>();
        for (var importEntry : virtualImportCalMap.entrySet()) {
            String virtualSku = importEntry.getKey();
            DeliveryCalResultDto deliveryCalResultDto = deliveryResult.get(virtualSku);
            DeliveryCalResultDto normalDeliveryResultDto = narmalDeliveryMap.get(virtualSku);

            // 海外仓冗余和工厂冗余
            RedundancyDp foreignRedundancy = foreignRedundancyMap.getOrDefault(virtualSku, new RedundancyDp(virtualSku));
            RedundancyDp onShippingRedundancy = onShippingRedundancyMap.getOrDefault(virtualSku, new RedundancyDp(virtualSku));
            RedundancyDp factoryRedundancy = factoryRedundancyMap.getOrDefault(virtualSku, new RedundancyDp(virtualSku));

            // 海外仓售罄时间
            LocalDate soldOutDate = soldOutDateMap.get(virtualSku);
            List<SoldOutDateBeforeTheoryDto> soldOutDateDtoList = soldOutDetailMap.getOrDefault(virtualSku, new ArrayList<>());
            SalableDateDto salableDate = salableDateMap.getOrDefault(virtualSku, new SalableDateDto());

            // 全链路海外仓售罄时间
            LocalDate fullLinkSoldOutDate = fullLinkSoldOutDateMap.get(virtualSku);
            List<SoldOutDateBeforeTheoryDto> fullLinkSoldOutDateDtoList = fullLinkSoldOutDetailMap.getOrDefault(virtualSku, new ArrayList<>());
            SalableDateDto fullLinkSalableDate = fullLinkSalableDateMap.getOrDefault(virtualSku, new SalableDateDto());

            AchievementRateDto achievementRateDto = achievementRate.get(virtualSku);

            // 保存单个虚拟商品的冗余库存信息
            var saveDto = redundancySaveService.saveSingleVirtualSkuRedundancyInfo(importEntry, deliveryCalResultDto,
                    normalDeliveryResultDto, foreignRedundancy, onShippingRedundancy, factoryRedundancy, soldOutDate,
                    fullLinkSoldOutDate, solidDate, soldOutDateDtoList, headShippingDateMap, importFileId,
                    fullLinkSoldOutDateDtoList, salableDate, fullLinkSalableDate, achievementRateDto);
            deliveryInventoryList.add(saveDto);

            // 发送mq，计算无计划库存预警
            unsalableInventoryCalFormList.add(UnsalableInventoryCalForm.builder()
                    .inventoryInfoId(saveDto.getInventoryId())
                    .virtualSkuId(importEntry.getValue().virtualProduct().getId())
                    .normalDeliveryResultDto(normalDeliveryResultDto)
                    .factoryPlanInfoList(saveDto.getFactoryFinishedInventoryList())
                    .remainInventoryList(saveDto.getInventoryForeignStoreList())
                    .build());

            // 发送mq，计算加急补货
            urgentPurchaseMQFormList.add(UrgentPurchaseMQForm.builder()
                    .inventoryInfoId(saveDto.getInventoryId())
                    .snapShotId(normalDeliveryResultDto.getSnapShotId())
                    .targetSalesMap(importEntry.getValue().targetSales().targetSalesMap())
                    .salesRules(saveDto.getRulesDO())
                    .calFinishedDate(LocalDate.now())
                    .normalDeliveryResultDto(normalDeliveryResultDto)
                    .build());
        }

        // 保存正常的发货数据
        NormalDeliveryInventoryForm form = NormalDeliveryInventoryForm.builder().needDelete(true).list(deliveryInventoryList).build();
        deliveryInventoryRepository.saveNormalDeliveryInventory(form);

        for (int i = 0; i < unsalableInventoryCalFormList.size(); i++) {
            UnsalableInventoryCalForm unsalableInventoryCalForm = unsalableInventoryCalFormList.get(i);
            rocketMQTemplate.convertAndSend(UNSALABLE_INVENTORY_WARNING_TOPIC, unsalableInventoryCalForm);

            // 只计算正常品和新品不断货状态的产品
            if (i < urgentPurchaseMQFormList.size()) {
                UrgentPurchaseMQForm urgentPurchaseMQForm = urgentPurchaseMQFormList.get(i);
                rocketMQTemplate.convertAndSend(URGENT_PURCHASE_TOPIC, urgentPurchaseMQForm);
            }
        }
    }

    public DeliveryCalculationForm convertToRequestForm(List<InventoryCalDp> inventoryCalDps, DateTime dateTime,
                                                        boolean needFactoryPlan, boolean needOnShippingInventory, boolean needToTargetEndDate,
                                                        Map<String, Integer> headShippingDaysMap, int redundantDate) {
        if (headShippingDaysMap.isEmpty()) {
            throw new NullPointerException("头程时间为空，请联系开发人员在缓存中添加好头程时间");
        }
        LocalDate localDate = DateUtils.convertToLocalDate(dateTime);
        List<DeliveryCalRequestDto> deliveryCalRequestList = new ArrayList<>();
        for (InventoryCalDp dp : inventoryCalDps) {
            InventoryRulesDp inventoryRulesDp = dp.inventoryRules();
            InvOrRepDaysRulesDp daysRulesDp = inventoryRulesDp.daysRulesDp();
            ShippingRulesDp shippingRatioDp = inventoryRulesDp.shippingRulesDp();
            StockQuantityDp quantityDp = dp.stockQuantity();
            String virtualSku = dp.virtualProduct().getVirtualSku();
            Map<String, BigDecimal> targetSalesMap = dp.targetSales().targetSalesMap();

            // 计算截止日期
            LocalDate endDate;
            if (!needToTargetEndDate) {
                endDate = redundancyEndCalService.calRedundancyEndDate(dp, localDate, headShippingDaysMap, redundantDate);
            } else {
                endDate = targetSalesMap.keySet().stream()
                        .map(key -> DateUtils.convertToLocalDate(key, YYYY_MM_DD_DATE_FORMAT_SLASH))
                        .max(Comparator.comparing(k -> k))
                        .orElse(DateUtils.convertToLocalDate(DateUtil.endOfMonth(dateTime)));
            }

            String minShippingDateWarehouse = shippingRatioDp.shippingRatioMap().entrySet().stream()
                    .filter(entry -> entry.getValue() > 0)
                    .min(Map.Entry.comparingByValue())
                    .orElseThrow(() -> new RuntimeException(String.format("虚拟sku：%s 缺少发货比例", virtualSku)))
                    .getKey();
            int minHeadShippingDate = headShippingDaysMap.get(minShippingDateWarehouse);
            LocalDate shippingEndDate = endDate.minusDays(minHeadShippingDate);
            List<FactoryRemainInventoryDto> remainInventoryList = quantityDp.list().stream()
                    .filter(f -> DateUtil.dateNew(f.getEnableUsingDate()).compareTo(DateUtil.beginOfDay(dateTime)) == 0)
                    .toList();
            DeliveryCalRequestDto deliveryCalRequestDto = DeliveryCalRequestDto.builder()
                    .virtualSku(virtualSku)
                    .safeDays(daysRulesDp.safeDays())
                    .changeableSafeDays(redundantDate)
                    .transitDays(daysRulesDp.transitDays())
                    .shippingCircle(daysRulesDp.shippingCircle())
                    .calEndDate(endDate)
                    .shippingEndDateStr(shippingEndDate.format(formatter))
                    .factoryRemainInventoryList(needOnShippingInventory ? quantityDp.list() : remainInventoryList)
                    .priorDeliveryList(new ArrayList<>())
                    .finishedInventoryList(needFactoryPlan ? dp.factoryPlan().factoryFinishedInventoryDtos() : new ArrayList<>())
                    .headShippingDays(headShippingDaysMap)
                    .shippingRatio(shippingRatioDp.shippingRatioMap())
                    .containLoader(dp.selfProduct().getContainerLoad())
                    .targetSalesMap(targetSalesMap)
                    .build();
            deliveryCalRequestList.add(deliveryCalRequestDto);
        }
        return DeliveryCalculationForm.builder()
                .shippingStartDateStr(DateUtil.format(dateTime, YYYY_MM_DD_DATE_FORMAT_SLASH))
                .deliveryCalRequestDtoList(deliveryCalRequestList)
                .build();
    }

    private List<InventoryCalDp> getInventoryCalDpList(ImportInventoryRulesDto inventoryRulesDto,
                                                       ImportTargetSalesDto importTargetSalesDto,
                                                       Map<String, FactoryPlanDp> factoryPlanDpMap,
                                                       Map<String, StockQuantityDp> stockQuantityDpMap,
                                                       Map<String, AmStockQuantityDp> amStockQuantityDpMap) {
        var inventoryRulesDpMap = inventoryRulesDto.getInventoryRulesDpMap();
        var inventoryRulesUpgradeMap = inventoryRulesDto.getInventoryRulesUpgradeMap();

        var destinationSalesDpMap = importTargetSalesDto.getDestinationSalesDtoMap();
        var targetSalesUpgradeMap = importTargetSalesDto.getTargetSalesUpgradeMap();
        // 判断导入数据是否为空
        if (inventoryRulesDpMap.isEmpty() && destinationSalesDpMap.isEmpty() && factoryPlanDpMap.isEmpty() && stockQuantityDpMap.isEmpty() && amStockQuantityDpMap.isEmpty()) {
            throw new RuntimeException("导入数据为空");
        }

        // 判断备货规则里的商品是否都有目标日销、
        Set<String> rulesSkuSet = new HashSet<>(inventoryRulesDpMap.keySet());
        Set<String> targetSalesSet = destinationSalesDpMap.keySet();
        if (!targetSalesSet.containsAll(rulesSkuSet)) {
            rulesSkuSet.removeAll(targetSalesSet);
            throw new IllegalArgumentException(String.format("备货规则里下列商品没有实际日销：%s", rulesSkuSet));
        }

        // 判断备货规则，目标日销中的升级关系
        StringBuilder error = new StringBuilder();
        for (var upgrade : inventoryRulesUpgradeMap.entrySet()) {
            if (!inventoryRulesDpMap.containsKey(upgrade.getValue())) {
                error.append(String.format("备货规则中未找到被升级款：%s 对应的升级款：%s；\n", upgrade.getKey(), upgrade.getValue()));
            }
        }

        for (var upgrade : targetSalesUpgradeMap.entrySet()) {
            if (!destinationSalesDpMap.containsKey(upgrade.getValue())) {
                error.append(String.format("实际日销中未找到被升级款：%s 对应的升级款：%s；\n", upgrade.getKey(), upgrade.getValue()));
            }
        }

        if (StrUtil.isNotBlank(error.toString())) {
            throw new IllegalArgumentException(error.toString());
        }

        Map<String, StockQuantityDp> stockQuantityMap = new HashMap<>();
        Set<String> stockQuantitySet = stockQuantityDpMap.keySet();
        Set<String> amStockQuantitySet = amStockQuantityDpMap.keySet();
        Set<String> combinedSet = new HashSet<>(stockQuantitySet);
        combinedSet.addAll(amStockQuantitySet);

        combinedSet.forEach(virtualSku -> {
            StockQuantityDp newStockQuantityDp = new StockQuantityDp(virtualSku, new ArrayList<>());
            StockQuantityDp stockQuantityDp = stockQuantityDpMap.getOrDefault(virtualSku, new StockQuantityDp(virtualSku, new ArrayList<>()));
            AmStockQuantityDp amStockQuantityDp = amStockQuantityDpMap.getOrDefault(virtualSku, new AmStockQuantityDp(virtualSku, new ArrayList<>()));
            if (ObjectUtil.isNotEmpty(stockQuantityDp)) {
                newStockQuantityDp.addStockQuantityAll(stockQuantityDp.list());
            }
            if (ObjectUtil.isNotEmpty(amStockQuantityDp)) {
                newStockQuantityDp.addStockQuantityAll(amStockQuantityDp.list());
            }
            stockQuantityMap.put(virtualSku, newStockQuantityDp);
        });

        // 组装待试算的数据
        List<InventoryCalDp> inventoryCalDpList = new ArrayList<>(inventoryRulesDpMap.size());
        List<String> virtualSkuList = inventoryRulesDpMap.keySet().stream().toList();
        List<VirtualProductDO> virtualProductList = virtualProductRepository.getListByVirtualSkuList(virtualSkuList);
        List<String> virtualIdList = virtualProductList.stream().map(VirtualProductDO::getId).toList();

        List<String> selfIdList = virtualProductList.stream().map(VirtualProductDO::getSelfProductSkuId).toList();
        List<SelfProductDO> selfProductList = selfProductRepositoryImpl.getSelfProductBySelfIdList(selfIdList);
        Map<String, SelfProductDO> selfMap = selfProductList.stream().collect(Collectors.toMap(SelfProductDO::getId, s -> s));

        // 准备目标日销
        LocalDate twoMonthBefore = LocalDate.now().minusMonths(2).withDayOfMonth(1);
        targetSalesService.prepareTargetSales(virtualIdList, twoMonthBefore);

        for (var virtualProduct : virtualProductList) {
            String virtualSku = virtualProduct.getVirtualSku();

            // 备货规则
            InventoryRulesDp inventoryRulesDp = inventoryRulesDpMap.get(virtualSku);
            // 目标日销
            Map<String, BigDecimal> targetSalesMap = targetSalesService.getTargetSales(virtualProduct.getId(), twoMonthBefore);
            TargetSalesDp targetSalesDp = new TargetSalesDp(virtualSku, targetSalesMap);
            // 工厂计划
            FactoryPlanDp factoryPlanDp = factoryPlanDpMap.getOrDefault(virtualSku, new FactoryPlanDp(virtualSku, new ArrayList<>()));
            factoryPlanDp.factoryFinishedInventoryDtos()
                    .sort(Comparator.comparing(FactoryFinishedInventoryDto::getFactoryFinishedDate)
                            .thenComparing(c -> targetSalesUpgradeMap.containsKey(c.getVirtualSku()))
                            .thenComparing(c -> c.getVirtualSku().equals(virtualProduct.getOldSku()))
                            .thenComparing(FactoryFinishedInventoryDto::getContractCode)
                    );

            // 海外库存
            StockQuantityDp stockQuantityDp = stockQuantityMap.getOrDefault(virtualSku, new StockQuantityDp(virtualSku, new ArrayList<>()));

            inventoryCalDpList.add(new InventoryCalDp(virtualProduct, inventoryRulesDp, targetSalesDp, stockQuantityDp,
                    factoryPlanDp, selfMap.get(virtualProduct.getSelfProductSkuId()), destinationSalesDpMap.get(virtualSku)));
        }

        return inventoryCalDpList;
    }

    private void checkSheet(int sheetNo, List<ReadSheet> readSheets) {
        redundantImportSheetNameEnum[] values = redundantImportSheetNameEnum.values();
        if (readSheets.size() < values.length || !readSheets.get(sheetNo).getSheetName().equals(values[sheetNo].getSheetName())) {
            throw new RuntimeException("请检查sheet是否正确");
        }
    }

    private CompletableFuture<ImportInventoryRulesDto> getRulesFuture(InputStream file, String fileName, List<String> errorList) {
        return CompletableFuture.supplyAsync(() -> {
            ExcelReader excelReader = null;

            try {
                excelReader = EasyExcel.read(file).build();
                ImportInventoryRulesDto inventoryRulesDto = new ImportInventoryRulesDto();
                Map<String, InventoryRulesDp> inventoryRulesDpMap = new HashMap<>();
                Map<String, String> inventoryRulesUpgradeMap = new HashMap<>();
                inventoryRulesDto.setInventoryRulesDpMap(inventoryRulesDpMap);
                inventoryRulesDto.setInventoryRulesUpgradeMap(inventoryRulesUpgradeMap);
                List<ReadSheet> readSheets = excelReader.excelExecutor().sheetList();
                int sheetNo = redundantImportSheetNameEnum.RULES.getSheetNo();
                checkSheet(sheetNo, readSheets);
                ReadSheet readSheet = EasyExcel
                        .readSheet(sheetNo)
                        .headRowNumber(1)
                        .registerReadListener(new RedundantInventoryRulesImportListener(fileName, errorList, inventoryRulesDto))
                        .autoTrim(true)
                        .build();
                excelReader.read(readSheet);

                return inventoryRulesDto;
            } finally {
                IOUtils.closeIOStream(excelReader);
            }
        }, importExcelThreadPool);
    }

    private CompletableFuture<ImportTargetSalesDto> getTargetSalesFuture(InputStream file, String fileName, List<String> errorList) {
        return CompletableFuture.supplyAsync(() -> {
            ExcelReader excelReader = null;

            try {
                excelReader = EasyExcel.read(file).build();
                List<ReadSheet> readSheets = excelReader.excelExecutor().sheetList();
                int sheetNo = redundantImportSheetNameEnum.DESTINATION_SALES.getSheetNo();
                checkSheet(sheetNo, readSheets);
                ImportTargetSalesDto importTargetSalesDto = new ImportTargetSalesDto();
                Map<String, ImportActualDestinationSalesDto> destinationSalesDpMap = new HashMap<>();
                Map<String, String> targetSalesUpgradeMap = new HashMap<>();
                importTargetSalesDto.setDestinationSalesDtoMap(destinationSalesDpMap);
                importTargetSalesDto.setTargetSalesUpgradeMap(targetSalesUpgradeMap);
                ReadSheet readSheet = EasyExcel
                        .readSheet(sheetNo)
                        .head(RedundantDestinationSalesExcel.class)
                        .registerReadListener(new InventoryTargetSalesImportListener(fileName, errorList, importTargetSalesDto))
                        .autoTrim(true)
                        .build();
                excelReader.read(readSheet);
                return importTargetSalesDto;
            } finally {
                IOUtils.closeIOStream(excelReader);
            }
        }, importExcelThreadPool);
    }

    private CompletableFuture<Map<String, FactoryPlanDp>> getFactoryPlanFuture(InputStream file, String fileName, List<String> errorList) {
        return CompletableFuture.supplyAsync(() -> {
            ExcelReader excelReader = null;

            try {
                excelReader = EasyExcel.read(file).build();
                List<ReadSheet> readSheets = excelReader.excelExecutor().sheetList();
                int sheetNo = redundantImportSheetNameEnum.PLAN.getSheetNo();
                checkSheet(sheetNo, readSheets);
                Map<String, FactoryPlanDp> factoryPlanDpMap = new HashMap<>();
                ReadSheet readSheet = EasyExcel
                        .readSheet(sheetNo)
                        .head(RedundantInventoryFactoryPlanExcel.class)
                        .registerReadListener(new FactoryPlanImportListener(fileName, errorList, factoryPlanDpMap))
                        .autoTrim(true)
                        .build();
                excelReader.read(readSheet);
                return factoryPlanDpMap;
            } finally {
                IOUtils.closeIOStream(excelReader);
            }
        }, importExcelThreadPool);
    }

    private CompletableFuture<Map<String, StockQuantityDp>> getStockQuantityFuture(InputStream file, String fileName,
                                                                                   List<String> errorList) {
        return CompletableFuture.supplyAsync(() -> {
            ExcelReader excelReader = null;

            try {
                excelReader = EasyExcel.read(file).build();
                List<ReadSheet> readSheets = excelReader.excelExecutor().sheetList();
                int sheetNo = STOCK_QUANTITY.getSheetNo();
                checkSheet(sheetNo, readSheets);
                Map<String, StockQuantityDp> stockQuantityDpMap = new HashMap<>();
                ReadSheet readSheet = EasyExcel
                        .readSheet(sheetNo)
                        .head(InventoryStockQuantityInfoExcel.class)
                        .registerReadListener(new InventoryStockQuantityImportListener(fileName, errorList, stockQuantityDpMap))
                        .autoTrim(true)
                        .build();
                excelReader.read(readSheet);
                return stockQuantityDpMap;
            } finally {
                IOUtils.closeIOStream(excelReader);
            }
        }, importExcelThreadPool);
    }

    private CompletableFuture<Map<String, AmStockQuantityDp>> getAmStockQuantityFuture(InputStream file, String fileName,
                                                                                       List<String> errorList, DateTime dateTime) {
        return CompletableFuture.supplyAsync(() -> {
            ExcelReader excelReader = null;

            List<SenboWarehouseDto> senboWarehouseDto = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
            try {
                excelReader = EasyExcel.read(file).build();
                List<ReadSheet> readSheets = excelReader.excelExecutor().sheetList();
                checkSheet(AM_STOCK_QUANTITY.getSheetNo(), readSheets);
                checkSheet(LAMP_STOCK_QUANTITY.getSheetNo(), readSheets);

                Map<String, String> channelMapping = channelRedisRepository.getChannelMapping();
                Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();
                if (CollectionUtil.isEmpty(channelMapping)) {
                    channelMapping = CHANNEL_MAPPING;
                }
                Map<String, AmStockQuantityDp> stockQuantityDpMap = new HashMap<>();
                ReadSheet readSheet1 = EasyExcel
                        .readSheet(AM_STOCK_QUANTITY.getSheetNo())
                        .headRowNumber(1)
                        .registerReadListener(
                                new InventoryStockQuantityOtherImportListener(fileName, errorList, senboWarehouseDto, stockQuantityDpMap, dateTime, channelMapping, channelIdNameMap)
                        )
                        .autoTrim(true)
                        .build();
                ReadSheet readSheet2 = EasyExcel
                        .readSheet(LAMP_STOCK_QUANTITY.getSheetNo())
                        .headRowNumber(1)
                        .registerReadListener(
                                new InventoryStockQuantityOtherImportListener(fileName, errorList, senboWarehouseDto, stockQuantityDpMap, dateTime, channelMapping, channelIdNameMap))
                        .autoTrim(true)
                        .build();
                excelReader.read(readSheet1, readSheet2);
                return stockQuantityDpMap;
            } finally {
                IOUtils.closeIOStream(excelReader);
            }
        }, importExcelThreadPool);
    }

    @Override
    public IPage<InventoryInfoDto> pageList(InventoryInfoQuery form) {
        return selectListByInfoIds(form);
    }




    @Override
    public DetailInventoryRulesDto getDetailInventoryRules(String inventoryInfoId) {
        RedundantInventoryDO inventoryInfoDO = redundantInventoryRepository.getById(inventoryInfoId);
        if (ObjectUtil.isEmpty(inventoryInfoDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "库存数据不存在");
        }
        SelfAndVirtualBO selfAndVirtualA = selfProductService.getSelfProductByVirtualId(inventoryInfoDO.getVirtualSkuId());

        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        Map<Integer, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));
        Map<String, Integer> nameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));

        List<String> inventoryInfoIds = Collections.singletonList(inventoryInfoId);
        List<InventorySaleRulesDO> inventorySaleList = inventorySaleRulesService.getInventorySaleByInfoIds(inventoryInfoIds);
        InventorySaleRulesDO saleRules = inventorySaleList.getFirst();

        InventoryRulesBO baseParamDto = redundantInventoryAssembler.saleRulesDOToInventoryRulesA(saleRules);
        baseParamDto.setPurchaseDate(saleRules.getProduceDays());
        TreeMap<String, Integer> headShippingDateMap = new TreeMap<>(baseParamDto.getHeadShippingDays());
        TreeMap<String, Double> saleRatioMap = new TreeMap<>(baseParamDto.getShippingRatio());

        // 排序
        Map<String, Integer> sortedHeadShippingDays = new LinkedHashMap<>();
        Map<String, Double> sortedShippingRatioDays = new LinkedHashMap<>();

        // 创建一个映射，用于快速查找仓库在列表中的索引
        Map<String, Integer> warehouseIndexMap = new HashMap<>();
        for (int i = 0; i < senboWarehouseList.size(); i++) {
            warehouseIndexMap.put(String.valueOf(senboWarehouseList.get(i).getSenboWarehouseId()), i);
        }

        // 分组并排序
        Map<String, Double> sortedEntries = saleRatioMap.entrySet().stream()
                .collect(Collectors.partitioningBy(e -> e.getValue() > 0))
                .entrySet().stream()
                .sorted((e1, e2) -> Boolean.compare(e2.getKey(), e1.getKey())) // true (> 0) 在前
                .flatMap(entry -> entry.getValue().stream()
                        .sorted((e1, e2) -> {
                            Integer index1 = warehouseIndexMap.get(e1.getKey());
                            Integer index2 = warehouseIndexMap.get(e2.getKey());
                            return Integer.compare(
                                    index1 != null ? index1 : Integer.MAX_VALUE,
                                    index2 != null ? index2 : Integer.MAX_VALUE
                            );
                        }))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));

        // 填充排序后的映射
        for (Map.Entry<String, Double> entry : sortedEntries.entrySet()) {
            String warehouseId = entry.getKey();
            String warehouseName = idNameMap.get(Integer.parseInt(warehouseId));
            sortedShippingRatioDays.put(warehouseName, entry.getValue());

            if (headShippingDateMap.containsKey(warehouseId)) {
                sortedHeadShippingDays.put(warehouseName, headShippingDateMap.get(warehouseId));
            }
        }
        baseParamDto.setHeadShippingDays(sortedHeadShippingDays);
        baseParamDto.setShippingRatio(sortedShippingRatioDays);

        //海外库存
        List<InventoryForeignStoreDO> foreignStoreList = inventoryForeignStoreService.getForeignStoreListByInfoIds(inventoryInfoIds);
        Set<String> containsInventorySet = new HashSet<>();
        foreignStoreList.forEach(k -> containsInventorySet.add(nameIdMap.get(k.getWarehouse()).toString()));

        // 获取发货数据
        List<InventoryFactoryPlanInfoA> planInfoList = inventoryFactoryPlanInfoService.getPlanInfoListByInfoId(inventoryInfoId);
        List<String> factoryInventoryIdList = planInfoList.stream().map(InventoryFactoryPlanInfoA::getId).toList();
        TrialWatchBoardVo deliveryInventory = deliveryInventoryRepository.getNormalDeliveryInventory(factoryInventoryIdList);
        List<ShippingProjectWatchBoardWarehouseDto> watchBoardWarehouseList = deliveryInventory.getWatchBoardWarehouseList();
        Set<String> deliveryWarehouseSet = watchBoardWarehouseList.stream().map(ShippingProjectWatchBoardWarehouseDto::getWarehouse).collect(Collectors.toSet());

        // 遍历 shippingRatioMap,找出值为 0 的键
        Set<String> keysToRemove = saleRatioMap.entrySet().stream()
                .filter(entry -> entry.getValue() == 0)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
        keysToRemove.removeAll(deliveryWarehouseSet);
        keysToRemove.removeAll(containsInventorySet);
        List<String> sortedWarehouseList = sortedEntries.keySet().stream()
                .filter(aDouble -> !keysToRemove.contains(aDouble))
                .map(m -> idNameMap.get(Integer.valueOf(m)))
                .toList();

        LocalDate startDate = inventoryInfoDO.getCalFinishedDate();
        TreeMap<String, Double> monthTargetSalesMap = calTargetSalesMap(saleRules.getSaleDestination(), startDate);

        return DetailInventoryRulesDto.builder()
                .baseParamDto(baseParamDto)
                .warehouseSortList(sortedWarehouseList)
                .destinationEverydaySale(monthTargetSalesMap)
                .virtualSku(selfAndVirtualA.getVirtualSku())
                .productName(selfAndVirtualA.getProductName())
                .build();
    }

    private TreeMap<String, Double> calTargetSalesMap(String saleDestination, LocalDate startDate) {
        var targetSalesMap = JSON.parseObject(saleDestination, new TypeReference<TreeMap<String, Double>>() {
        });
        TreeMap<String, Double> monthTargetSalesMap = new TreeMap<>(Comparator.comparing(c -> YearMonth.parse(c, chineseFormatter)));

        LocalDate endDate;
        LocalDate startDateMonthLast = startDate.with(TemporalAdjusters.lastDayOfMonth());
        if (CollectionUtil.isEmpty(targetSalesMap)) {
            endDate = startDateMonthLast;
        } else {
            LocalDate parse = LocalDate.parse(targetSalesMap.lastKey(), formatter);
            endDate = parse.isAfter(startDateMonthLast) ? parse : startDateMonthLast;
        }
        for (var calDate = startDate.minusMonths(2).withDayOfMonth(1); !calDate.isAfter(endDate); calDate = calDate.plusDays(1)) {
            String date = calDate.format(formatter);
            targetSalesMap.putIfAbsent(date, 0D);
            YearMonth yearMonth = YearMonth.from(calDate);
            Double daySales = targetSalesMap.getOrDefault(date, 0D);
            monthTargetSalesMap.compute(yearMonth.format(chineseFormatter),
                    (k, v) -> BigDecimal.valueOf(Optional.ofNullable(v).orElse(0D) + daySales).setScale(3, HALF_UP).doubleValue());
        }
        return monthTargetSalesMap;
    }

    @Override
    public DetailInventoryRulesDto getShippingCalculation(String inventoryInfoId) {
        RedundantInventoryDO inventoryInfoDO = redundantInventoryRepository.getById(inventoryInfoId);
        if (ObjectUtil.isEmpty(inventoryInfoDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "库存数据不存在");
        }
        SelfAndVirtualBO selfAndVirtualA = selfProductService.getSelfProductByVirtualId(inventoryInfoDO.getVirtualSkuId());

        List<String> inventoryInfoIds = Collections.singletonList(inventoryInfoId);

        //海外库存
        List<InventoryForeignStoreDO> foreignStoreList = inventoryForeignStoreService.getForeignStoreListByInfoIds(inventoryInfoIds);
        List<InventoryForeignStoreBO> enableUseInventoryList = new ArrayList<>();
        List<InventoryForeignStoreBO> onShippingInventoryList = new ArrayList<>();
        foreignStoreList.forEach(k -> {
            InventoryForeignStoreBO inventoryForeignStoreA = redundantInventoryAssembler.storeDOToForeignStoreA(k);
            inventoryForeignStoreA.setEnableUsingDateString(DateUtil.formatDate(k.getEnableUsingDate()));

            if (!LocalDateTimeUtil.of(k.getEnableUsingDate()).toLocalDate().isAfter(inventoryInfoDO.getCalFinishedDate())) {
                enableUseInventoryList.add(inventoryForeignStoreA);
            } else {
                onShippingInventoryList.add(inventoryForeignStoreA);
            }
        });

        List<InventoryFactoryPlanInfoA> planInfoList = inventoryFactoryPlanInfoService.getPlanInfoListByInfoIds(inventoryInfoIds);
        // 海外仓冗余、在途冗余试算看板
        List<InventoryForeignShipWatchBoardDO> inventoryWatchBoardList = inventoryWatchBoardService.getForeignAndShipInventoryWatchBoardByInfoId(inventoryInfoId);
        List<DetailInventoryWatchBoardDto> watchBoardDtoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(inventoryWatchBoardList)) {
            watchBoardDtoList = redundantInventoryAssembler.watchBoardDOToDto(inventoryWatchBoardList);

            Set<LocalDate> calculatedDate = new HashSet<>();
            for (var dto : watchBoardDtoList) {
                if (!calculatedDate.contains(dto.getArrivingDate())) {
                    List<String> shipmentCodeList = onShippingInventoryList.stream()
                            .filter(f -> DateUtils.convertToLocalDate(f.getEnableUsingDate()).equals(dto.getArrivingDate()))
                            .map(m -> Optional.ofNullable(m.getShipmentCode()).orElse(""))
                            .toList();
                    dto.setShipmentCodeList(shipmentCodeList);
                    calculatedDate.add(dto.getArrivingDate());
                }
            }
        }

        // 本地仓冗余试算看板
        List<InventoryLocalWatchBoardDO> factoryInventoryWatchBoardList = inventoryWatchBoardService.getFactoryInventoryWatchBoardByInfoId(inventoryInfoId);
        List<FactoryWatchBoardDto> localWatchBoardDtoList = new ArrayList<>();
        for (var localWatchBoard : factoryInventoryWatchBoardList) {
            localWatchBoardDtoList.add(FactoryWatchBoardDto.builder()
                    .initialInventory(localWatchBoard.getInitialInventory())
                    .judgeDate(localWatchBoard.getJudgeDate())
                    .localRedundancy(localWatchBoard.getLocalRedundancy())
                    .safeInventory(localWatchBoard.getSafeInventory())
                    .unShippingNum(localWatchBoard.getUnShippingNum())
                    .shippingNum(localWatchBoard.getShippingNum())
                    .totalRedundancy(localWatchBoard.getTotalRedundancy())
                    .watchBoardId(localWatchBoard.getId())
                    .build());
        }
        //模拟表格
        DeliveryCalculationVo deliveryCalculation = inventoryMockTableService.getInventoryMockTable(inventoryInfoDO);
        DeliveryCalResultDto resultDto = deliveryCalculation.getDeliveryCalResultList().getFirst();

        return DetailInventoryRulesDto.builder()
                .enableUseInventoryList(enableUseInventoryList)
                .onShippingInventoryList(onShippingInventoryList)
                .shippingDetailPlainDtoList(planInfoList)
                .calFinishedDate(inventoryInfoDO.getCalFinishedDate().format(formatter))
                .virtualSku(selfAndVirtualA.getVirtualSku())
                .watchBoardDtoList(watchBoardDtoList)
                .factoryWatchBoardDtoList(localWatchBoardDtoList)
                .mockShippingInventoryMap(resultDto.getEverydayOnShippingInventoryMap())
                .mockRemainInventoryMap(resultDto.getEverydayRemainInventoryMap())
                .mockDaysSaleInventoryMap(resultDto.getEverydaySaleProductMap())
                .endDateDesc(StrUtil.isBlank(inventoryInfoDO.getEndDateDesc()) ? "" : inventoryInfoDO.getEndDateDesc())
                .build();
    }

    @Override
    public NormalDeliveryWatchBoardVo getNormalDeliveryWatchBoard(String inventoryInfoId) {
        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        NormalDeliveryWatchBoardVo normalResult = getNormalShippingCalculation(inventoryInfoId, senboWarehouseList);
        normalResult.setDeliveryCalResultDto(null);
        List<ShippingProjectWatchBoardWarehouseDto> watchBoardWarehouseList = normalResult.getWatchBoardWarehouseList();
        if (CollectionUtil.isNotEmpty(watchBoardWarehouseList)) {
            List<InventoryLocalWatchBoardDO> localRedundancyList = inventoryWatchBoardService.getFactoryInventoryWatchBoardByInfoId(inventoryInfoId);
            InventoryLocalWatchBoardDO localRedundancy = localRedundancyList.getFirst();
            LocalDate judgeDate = localRedundancy.getJudgeDate();
            List<ShippingProjectWatchBoardWarehouseDto> list = watchBoardWarehouseList.stream()
                    .filter(f -> !DateUtils.convertToLocalDate(f.getArrivalDate(), YYYY_MM_DD_DATE_FORMAT_CHINESE).isAfter(judgeDate))
                    .toList();
            normalResult.setWatchBoardWarehouseList(list);
        }
        return normalResult;
    }

    public DetailInventoryRulesDto getShippingCalculationWithoutFactoryPlan(String inventoryInfoId) {
        // 获取冗余库存商品相关信息
        RedundantInventoryDO inventoryInfoDO = redundantInventoryRepository.getById(inventoryInfoId);
        if (ObjectUtil.isEmpty(inventoryInfoDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "库存数据不存在");
        }
        VirtualProductDO virtualProduct = virtualProductRepository.getById(inventoryInfoDO.getVirtualSkuId());
        SelfProductDO selfProduct = selfProductService.getSelfProductByVirtualSku(virtualProduct.getVirtualSku());
        List<String> inventoryInfoIds = Collections.singletonList(inventoryInfoId);
        List<InventorySaleRulesDO> inventorySaleList = inventorySaleRulesService.getInventorySaleByInfoIds(inventoryInfoIds);
        InventorySaleRulesDO inventorySaleRulesDO = inventorySaleList.getFirst();
        InventoryRulesBO baseParamDto = redundantInventoryAssembler.saleRulesDOToInventoryRulesA(inventorySaleRulesDO);
        baseParamDto.setPurchaseDate(selfProduct.getPurchaseDate());

        TreeMap<String, BigDecimal> targetSalesMap = JSONObject.parseObject(inventorySaleRulesDO.getSaleDestination(), new TypeReference<>() {
        });

        Map<String, Double> saleRatio = JSONObject.parseObject(inventorySaleRulesDO.getSaleRatio(), new TypeReference<>() {
        });
        //数据组装
        String virtualSku = virtualProduct.getVirtualSku();
        InventoryRulesDp inventoryRules = new InventoryRulesDp(
                virtualSku,
                new InvOrRepDaysRulesDp(inventorySaleRulesDO.getPurchaseDays(), inventorySaleRulesDO.getTransitDays(),
                        inventorySaleRulesDO.getSafeDays(), inventorySaleRulesDO.getShippingCircle(), inventorySaleRulesDO.getPurchaseCircle()),
                new ShippingRulesDp(saleRatio));

        TargetSalesDp targetSalesDp = new TargetSalesDp(virtualSku, targetSalesMap);

        //海外库存
        List<InventoryForeignStoreDO> foreignStoreList = inventoryForeignStoreService.getForeignStoreListByInfoIds(inventoryInfoIds);
        List<InventoryForeignStoreBO> enableUseInventoryList = new ArrayList<>();
        List<InventoryForeignStoreBO> onShippingInventoryList = new ArrayList<>();
        foreignStoreList.forEach(k -> {
            InventoryForeignStoreBO inventoryForeignStoreA = redundantInventoryAssembler.storeDOToForeignStoreA(k);
            inventoryForeignStoreA.setEnableUsingDateString(DateUtil.formatDate(k.getEnableUsingDate()));

            if (!LocalDateTimeUtil.of(k.getEnableUsingDate()).toLocalDate().isAfter(inventoryInfoDO.getCalFinishedDate())) {
                enableUseInventoryList.add(inventoryForeignStoreA);
            } else {
                onShippingInventoryList.add(inventoryForeignStoreA);
            }
        });

        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        Map<String, Integer> nameIdMap = senboWarehouseList.stream()
                .collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));
        // 使用Stream API进行转换
        List<FactoryRemainInventoryDto> factoryRemainInventoryList = foreignStoreList.stream()
                .map(foreignStore -> {
                    FactoryRemainInventoryDto factoryRemainInventory = new FactoryRemainInventoryDto();
                    factoryRemainInventory.setVirtualSku(virtualSku);
                    factoryRemainInventory.setWarehouse(nameIdMap.get(foreignStore.getWarehouse()).toString());
                    factoryRemainInventory.setEnableUsingDate(new DateTime(foreignStore.getEnableUsingDate()));
                    factoryRemainInventory.setStoreNum(foreignStore.getInventoryNum().doubleValue());
                    return factoryRemainInventory;
                }).collect(Collectors.toList());
        StockQuantityDp stockQuantity = new StockQuantityDp(virtualSku, factoryRemainInventoryList);
        List<InventoryCalDp> inventoryCalDps = new ArrayList<>();
        InventoryCalDp inventoryCalDp = new InventoryCalDp(virtualProduct, inventoryRules, targetSalesDp, stockQuantity,
                new FactoryPlanDp(virtualSku, new ArrayList<>()), selfProduct, null);
        inventoryCalDps.add(inventoryCalDp);
        DeliveryCalculationVo resultWithoutFactoryPlan = getShippingCalculation(inventoryInfoDO.getCalFinishedDate(),
                inventoryCalDps, false, true, baseParamDto.getChangeableSafeDays());

        List<DeliveryCalResultDto> deliveryCalResultList = resultWithoutFactoryPlan.getDeliveryCalResultList();
        DeliveryCalResultDto deliveryCalResultDto = new DeliveryCalResultDto();
        if (CollectionUtil.isNotEmpty(deliveryCalResultList)) {
            deliveryCalResultDto = deliveryCalResultList.getFirst();
        }

        // 判断是否是停售或者卖完停售的非流量子体
        VirtualProductDO snapshotVirtualInfo = snapshotRepository.getVirtualProductSnapshotBySnapshotId(inventoryInfoDO.getSnapshotId());
        Integer productStatus = snapshotVirtualInfo.getProductStatus();
        Integer subType = snapshotVirtualInfo.getSubType();
        StopSellingProductsRedundancyDto productsRedundancyDto = StopSellingProductsRedundancyDto.builder().build();
        if (Objects.equals(productStatus, STOP_SELLING.getCode()) || Objects.equals(productStatus, SOLD_OUT_STOP_SELLING.getCode())
                || Objects.equals(productStatus, NEW_ARRIVAL_TEST_SAMPLE.getCode()) || Objects.equals(productStatus, NEW_ARRIVAL_NORMAL_SAMPLE.getCode())) {
            // 本地仓冗余试算看板
            List<InventoryLocalWatchBoardDO> factoryInventoryWatchBoardList = inventoryWatchBoardService.getFactoryInventoryWatchBoardByInfoId(inventoryInfoId);
            InventoryLocalWatchBoardDO localWatchBoard = factoryInventoryWatchBoardList.getFirst();

            List<InventoryForeignShipWatchBoardDO> inventoryWatchBoardList = inventoryWatchBoardService.getForeignAndShipInventoryWatchBoardByInfoId(inventoryInfoId);
            List<DetailInventoryWatchBoardDto> watchBoardDtoList = redundantInventoryAssembler.watchBoardDOToDto(inventoryWatchBoardList);
            var watchBoard = watchBoardDtoList.getFirst();
            productsRedundancyDto.setForeignRedundancy(watchBoard.getRedundantNum());
            if (watchBoard.getArrivingDate() != null) {
                productsRedundancyDto.setArrivingDate(watchBoard.getArrivingDate().format(hyphenFormatter));
            }

            productsRedundancyDto.setOnShippingRedundancy(inventoryInfoDO.getOnShippingRedundantInventory());
            productsRedundancyDto.setFactoryRedundancy(localWatchBoard.getLocalRedundancy());
        }

        supplementMockTable(deliveryCalResultDto, inventoryInfoDO, targetSalesMap, senboWarehouseList, saleRatio);

        List<InventoryFactoryPlanInfoA> planInfoList = inventoryFactoryPlanInfoService.getPlanInfoListByInfoIds(inventoryInfoIds);
        return DetailInventoryRulesDto.builder()
                .enableUseInventoryList(enableUseInventoryList)
                .onShippingInventoryList(onShippingInventoryList)
                .shippingDetailPlainDtoList(planInfoList)
                .calFinishedDate(inventoryInfoDO.getCalFinishedDate().format(formatter))
                .virtualSku(virtualProduct.getVirtualSku())
                .productStatus(productStatus)
                .subType(subType)
                .stopSellingProductsRedundancy(productsRedundancyDto)
                .mockShippingInventoryMap(deliveryCalResultDto.getEverydayOnShippingInventoryMap())
                .mockRemainInventoryMap(deliveryCalResultDto.getEverydayRemainInventoryMap())
                .mockDaysSaleInventoryMap(deliveryCalResultDto.getEverydaySaleProductMap())
                .build();
    }

    private DeliveryCalculationVo getMockTable(RedundantInventoryDO inventoryInfoDO, VirtualProductDO virtualProduct,
                                               List<ShippingProjectWatchBoardWarehouseDto> watchBoardWarehouseList,
                                               List<SenboWarehouseDto> senboWarehouseList, SelfProductDO selfProduct) {
        String virtualSku = virtualProduct.getVirtualSku();
        String inventoryInfoId = inventoryInfoDO.getId();
        // 获取模拟试算表格
        LocalDate calDate = inventoryInfoDO.getCalFinishedDate();
        String calDateStr = calDate.format(formatter);
        // 发货规则
        InventorySaleRulesDO saleRules = inventorySaleRulesService.getInventorySaleByInfoId(inventoryInfoId);
        Map<String, Double> saleRatio = JSONObject.parseObject(saleRules.getSaleRatio(), new TypeReference<>() {
        });
        InventoryRulesDp inventoryRules = new InventoryRulesDp(virtualSku,
                new InvOrRepDaysRulesDp(saleRules.getPurchaseDays(), saleRules.getTransitDays(),
                        saleRules.getSafeDays(), saleRules.getShippingCircle(), saleRules.getPurchaseCircle()),
                new ShippingRulesDp(saleRatio));

        // 目标日销
        TreeMap<String, BigDecimal> targetSalesMap = JSONObject.parseObject(saleRules.getSaleDestination(), new TypeReference<>() {
        });

        TargetSalesDp targetSalesDp = new TargetSalesDp(virtualSku, targetSalesMap);
        Map<String, Integer> nameIdMap = senboWarehouseList.stream()
                .collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));

        List<InventoryForeignStoreDO> foreignStoreList = inventoryForeignStoreService.getForeignStoreListByInfoIds(Collections.singletonList(inventoryInfoId));
        List<FactoryRemainInventoryDto> factoryRemainInventoryList = foreignStoreList.stream()
                .map(foreignStore -> {
                    FactoryRemainInventoryDto factoryRemainInventory = new FactoryRemainInventoryDto();
                    factoryRemainInventory.setVirtualSku(virtualSku);
                    factoryRemainInventory.setWarehouse(nameIdMap.get(foreignStore.getWarehouse()).toString());
                    factoryRemainInventory.setEnableUsingDate(new DateTime(foreignStore.getEnableUsingDate()));
                    factoryRemainInventory.setStoreNum(foreignStore.getInventoryNum().doubleValue());
                    return factoryRemainInventory;
                }).collect(Collectors.toList());

        StockQuantityDp stockQuantity = new StockQuantityDp(virtualSku, factoryRemainInventoryList);
        InventoryCalDp inventoryCalDp = new InventoryCalDp(virtualProduct, inventoryRules, targetSalesDp, stockQuantity,
                new FactoryPlanDp(virtualSku, new ArrayList<>()), selfProduct, null);
        List<InventoryCalDp> inventoryCalDpList = Collections.singletonList(inventoryCalDp);
        Map<String, Integer> headShippingDaysMap = senboWarehouseList.stream()
                .collect(Collectors.toMap(s -> s.getSenboWarehouseId().toString(), SenboWarehouseDto::getHeadShippingDate));
        DateTime dateTime = DateUtils.convertToDateTime(inventoryInfoDO.getCalFinishedDate());
        DeliveryCalculationForm form = convertToRequestForm(inventoryCalDpList, dateTime, true,
                true, true, headShippingDaysMap, saleRules.getChangeableSafeDays());
        form.setCalculatedDateStr(calDateStr);
        // 计划里的发货到货
        form.getDeliveryCalRequestDtoList().getFirst().setDeliveryResultInventoryList(watchBoardWarehouseList.stream()
                .map(m -> {
                    FactoryRemainInventoryDto factoryRemainInventory = new FactoryRemainInventoryDto();
                    factoryRemainInventory.setVirtualSku(virtualSku);
                    factoryRemainInventory.setWarehouse(nameIdMap.get(m.getWarehouse()).toString());
                    factoryRemainInventory.setEnableUsingDate(new DateTime(m.getArrivalDate()));
                    factoryRemainInventory.setStoreNum(m.getShippingAmount().doubleValue());
                    return factoryRemainInventory;
                }).toList());

        // 获取模拟试算表格
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO result = restTemplateUtil.post(form, ResultDTO.class, DELIVERY_CALCULATE_MOCK_TABLE_URL);
        if (!Objects.equals(result.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("冗余库存计算时获取模拟试算表格异常，异常原因：{}", result.getMessage());
            throw new RuntimeException("冗余库存计算时获取模拟试算表格异常，异常原因：" + result.getMessage());
        }

        DeliveryCalculationVo deliveryCalculationVo = JSON.to(DeliveryCalculationVo.class, result.getData());
        supplementMockTable(deliveryCalculationVo.getDeliveryCalResultList().getFirst(), inventoryInfoDO, targetSalesMap, senboWarehouseList, saleRatio);
        return deliveryCalculationVo;
    }

    public void supplementMockTable(DeliveryCalResultDto deliveryCalResultDto, RedundantInventoryDO inventoryInfoDO,
                                    TreeMap<String, BigDecimal> targetSalesMap, List<SenboWarehouseDto> senboWarehouseList,
                                    Map<String, Double> saleRatio) {
        TreeMap<String, Map<String, Double>> mockRemainInventoryMap = deliveryCalResultDto.getEverydayRemainInventoryMap();
        TreeMap<String, Map<String, Double>> mockOnShippingInventoryMap = deliveryCalResultDto.getEverydayOnShippingInventoryMap();
        TreeMap<String, Map<String, Double>> mockDaysSaleInventoryMap = deliveryCalResultDto.getEverydaySaleProductMap();

        TreeMap<String, Map<String, Double>> newMockShippingMap = new TreeMap<>();
        TreeMap<String, Map<String, Double>> newMockRemainMap = new TreeMap<>();
        TreeMap<String, Map<String, Double>> newMockDaysSaleMap = new TreeMap<>();

        Map<Integer, String> idNameMap = senboWarehouseList.stream()
                .collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        // 获取两个日期之间的所有日期
        LocalDate endDate = inventoryInfoDO.getCalFinishedDate().with(TemporalAdjusters.lastDayOfMonth());
        String endDateStr = endDate.format(formatter);
        if (CollectionUtil.isNotEmpty(targetSalesMap)) {
            endDateStr = LocalDate.parse(targetSalesMap.lastKey(), formatter).isAfter(endDate) ? targetSalesMap.lastKey() : endDateStr;
        }
        List<String> datesBetween = getDatesBetween(inventoryInfoDO.getCalFinishedDate(), endDateStr, YYYY_MM_DD_DATE_FORMAT_HYPHEN);
        for (String date : datesBetween) {
            Date newDate = DateUtil.parse(date);
            String formatDate = DateUtil.format(newDate, YYYY_MM_DD_DATE_FORMAT_SLASH);
            Map<String, Double> onshippingInventoryMap = mockOnShippingInventoryMap.getOrDefault(formatDate, new HashMap<>());
            Map<String, Double> remainInventoryMap = mockRemainInventoryMap.getOrDefault(formatDate, new HashMap<>());
            Map<String, Double> daysSaleInventoryMap = mockDaysSaleInventoryMap.getOrDefault(formatDate, new HashMap<>());

            Map<String, Double> shippingMap = new LinkedHashMap<>();
            Map<String, Double> remainMap = new LinkedHashMap<>();
            Map<String, Double> daysSaleMap = new LinkedHashMap<>();

            // 排序
            senboWarehouseList.stream()
                    .sorted(Comparator.comparing(SenboWarehouseDto::getSort).reversed())
                    .forEach(warehouse -> {
                        Integer warehouseId = warehouse.getSenboWarehouseId();
                        String warehouseName = idNameMap.get(warehouseId);
                        shippingMap.put(warehouseName, onshippingInventoryMap.getOrDefault(warehouseId.toString(), 0D));
                        remainMap.put(warehouseName, remainInventoryMap.getOrDefault(warehouseId.toString(), 0D));
                        daysSaleMap.put(warehouseName, daysSaleInventoryMap.getOrDefault(warehouseId.toString(), 0D));
                    });
            String startShipping = "startShipping";
            String startSale = "startSale";
            shippingMap.put(startShipping, onshippingInventoryMap.get(startShipping));
            remainMap.put(startSale, remainInventoryMap.get(startSale));
            newMockShippingMap.put(date, shippingMap);
            newMockRemainMap.put(date, remainMap);
            newMockDaysSaleMap.put(date, daysSaleMap);
        }

        Set<String> zeroRatioSet = saleRatio.entrySet().stream()
                .filter(entry -> entry.getValue() == 0)
                .map(m -> idNameMap.get(Integer.parseInt(m.getKey()))).collect(Collectors.toSet());
        MockResultUtils.checkEmptyWarehouse(zeroRatioSet, newMockShippingMap);
        MockResultUtils.checkEmptyWarehouse(zeroRatioSet, newMockRemainMap);
        MockResultUtils.removeUselessMockResult(newMockRemainMap, newMockShippingMap, newMockDaysSaleMap, zeroRatioSet);

        // 将数据填充回去
        deliveryCalResultDto.setEverydayOnShippingInventoryMap(newMockShippingMap);
        deliveryCalResultDto.setEverydayRemainInventoryMap(newMockRemainMap);
        deliveryCalResultDto.setEverydaySaleProductMap(newMockDaysSaleMap);
    }

    @Override
    public NormalDeliveryWatchBoardVo getNormalShippingCalculation(String inventoryInfoId, List<SenboWarehouseDto> senboWarehouseList) {
        RedundantInventoryDO inventoryInfoDO = redundantInventoryRepository.getById(inventoryInfoId);

        if (ObjectUtil.isEmpty(inventoryInfoDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "库存数据不存在");
        }
        // 商品快照信息
        String snapshotId = inventoryInfoDO.getSnapshotId();
        ProductSnapshotDto productSnapshot = snapshotRepository.getProductSnapshotBySnapshotId(snapshotId);
        SelfProductDO selfProduct = JSON.to(SelfProductDO.class, productSnapshot.getSelfData());
        VirtualProductDO virtualProduct = JSON.to(VirtualProductDO.class, productSnapshot.getVirtualData());

        // 计划数据
        List<InventoryFactoryPlanInfoA> planInfoList = inventoryFactoryPlanInfoService.getPlanInfoListByInfoId(inventoryInfoId);
        List<String> factoryInventoryIdList = planInfoList.stream().map(InventoryFactoryPlanInfoA::getId).toList();
        Map<String, InventoryFactoryPlanInfoA> idFactoryPlanMap = planInfoList.stream().collect(Collectors.toMap(InventoryFactoryPlanInfoA::getId, a -> a));

        // 获取试算看板数据
        Map<Integer, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        TrialWatchBoardVo deliveryInventory = deliveryInventoryRepository.getNormalDeliveryInventory(factoryInventoryIdList);
        List<ShippingProjectWatchBoardWarehouseDto> watchBoardWarehouseList = deliveryInventory.getWatchBoardWarehouseList();
        for (var watchBoard : watchBoardWarehouseList) {
            InventoryFactoryPlanInfoA factoryPlanInfo = idFactoryPlanMap.get(watchBoard.getFactoryFinishedId());
            watchBoard.setWarehouse(idNameMap.get(Integer.valueOf(watchBoard.getWarehouse())));
            watchBoard.setContractCode(factoryPlanInfo.getContractCode());
            watchBoard.setFactoryFinishedDate(DateUtil.format(factoryPlanInfo.getFactoryFinishedDate(), formatter));
            watchBoard.setPerCaseNum(selfProduct.getContainerLoad());
            watchBoard.setOutCaseHeight(selfProduct.getCaseHeight());
            watchBoard.setOutCaseWidth(selfProduct.getCaseWidth());
            watchBoard.setOutCaseLength(selfProduct.getCaseLength());
            double volume = selfProduct.getCaseWidth() * selfProduct.getCaseHeight() * selfProduct.getCaseLength() * watchBoard.getPerCaseNum();
            watchBoard.setVolume(BigDecimal.valueOf(volume).divide(BigDecimal.valueOf(1000000), 2, HALF_UP).doubleValue());
            watchBoard.setWeight(BigDecimal.valueOf(selfProduct.getSingleCaseGrossWeight() * watchBoard.getCaseAmount()).setScale(3, HALF_UP).doubleValue());
            watchBoard.setDestinationSku(factoryPlanInfo.getDestinationSku());
            watchBoard.setDeliveryType(StrUtil.isNotBlank(factoryPlanInfo.getRemarks()) &&factoryPlanInfo.getRemarks().contains(URGENT.getDesc()) ? URGENT.getCode() : null);
        }
        watchBoardWarehouseList.sort(Comparator.comparing((ShippingProjectWatchBoardWarehouseDto c) -> c.getDeliveryType() != null && c.getDeliveryType().equals(URGENT.getCode())).reversed()
                .thenComparing((ShippingProjectWatchBoardWarehouseDto c) -> DateUtil.parse(c.getFactoryFinishedDate()))
                .thenComparing(c -> DateUtil.parse(c.getArrivalDate())));


        DeliveryCalculationVo deliveryCalculationVo = getMockTable(inventoryInfoDO, virtualProduct, watchBoardWarehouseList,
                senboWarehouseList, selfProduct);
        DeliveryCalResultDto deliveryCalResultDto = deliveryCalculationVo.getDeliveryCalResultList().getFirst();
        return NormalDeliveryWatchBoardVo.builder().watchBoardWarehouseList(watchBoardWarehouseList).deliveryCalResultDto(deliveryCalResultDto).build();
    }

    private void supplementEmptyMockTable(TreeMap<String, Map<String, Double>> everydayShippingInventoryMap,
                                          TreeMap<String, Map<String, Double>> everydayRemainInventoryMap,
                                          TreeMap<String, Map<String, Double>> everydaySaleInventoryMap,
                                          List<String> datesBetween) {
        List<SenboWarehouseDto> senboWarehouseDtoList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        List<Integer> warehouseIdList = senboWarehouseDtoList.stream().map(SenboWarehouseDto::getSenboWarehouseId).toList();
        for (String date : datesBetween) {
            Map<String, Double> shippingInventoryMap = everydayShippingInventoryMap.getOrDefault(date, new TreeMap<>());
            Map<String, Double> remainInventoryMap = everydayRemainInventoryMap.getOrDefault(date, new TreeMap<>());
            Map<String, Double> saleInventoryMap = everydaySaleInventoryMap.getOrDefault(date, new TreeMap<>());
            for (Integer western : warehouseIdList) {
                shippingInventoryMap.putIfAbsent(western.toString(), 0D);
                remainInventoryMap.putIfAbsent(western.toString(), 0D);
                saleInventoryMap.putIfAbsent(western.toString(), 0D);
            }
            everydayShippingInventoryMap.put(date, shippingInventoryMap);
            everydayRemainInventoryMap.put(date, remainInventoryMap);
            everydaySaleInventoryMap.put(date, saleInventoryMap);
        }
    }

    private void supplementEmptyMockTable(DeliveryCalculationVo deliveryCalculationVo, List<DeliveryCalRequestDto> deliveryCalRequestDtoList, LocalDate localDate) {
        List<DeliveryCalResultDto> deliveryCalResultList = deliveryCalculationVo.getDeliveryCalResultList();

        // 给空map补0，方便后续计算
        Map<String, Map<String, BigDecimal>> virtualSkuTargetSalesMap = deliveryCalRequestDtoList.stream()
                .collect(Collectors.toMap(DeliveryCalRequestDto::getVirtualSku, DeliveryCalRequestDto::getTargetSalesMap));

        for (DeliveryCalResultDto deliveryCalResult : deliveryCalResultList) {
            TreeMap<String, Map<String, Double>> everydayRemainInventoryMap = deliveryCalResult.getEverydayRemainInventoryMap();
            TreeMap<String, Map<String, Double>> everydayOnShippingInventoryMap = deliveryCalResult.getEverydayOnShippingInventoryMap();
            TreeMap<String, Map<String, Double>> everydaySaleProductMap = deliveryCalResult.getEverydaySaleProductMap();

            Map<String, BigDecimal> targetSalesMap = virtualSkuTargetSalesMap.get(deliveryCalResult.getVirtualSku());
            String endDateStr = targetSalesMap.keySet().stream().max(Comparator.comparing(k -> k)).orElse(localDate.format(formatter));
            List<String> datesBetween = getDatesBetween(localDate, endDateStr, YYYY_MM_DD_DATE_FORMAT_SLASH);
            supplementEmptyMockTable(everydayOnShippingInventoryMap, everydayRemainInventoryMap, everydaySaleProductMap, datesBetween);
        }
    }

    private List<String> getDatesBetween(LocalDate calFinishedDate, String endDateStr, String convertFormatter) {
        // 获取两个日期之间的所有日期
        LocalDate localDate = LocalDateTimeUtil.parseDate(endDateStr, YYYY_MM_DD_DATE_FORMAT_SLASH);
        LocalDate lastDayOfMonth = localDate.with(TemporalAdjusters.lastDayOfMonth());
        List<String> datesBetween = new ArrayList<>();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(convertFormatter);
        LocalDate currentDate = calFinishedDate;
        while (!currentDate.isAfter(lastDayOfMonth)) {
            datesBetween.add(currentDate.format(formatter));
            currentDate = currentDate.plusDays(1);
        }
        Collections.sort(datesBetween);
        return datesBetween;
    }

    @Override
    public Map<String, List<InventorySoldOutDaysDO>> getBeforeSoldOutList(List<String> inventoryInfoIds, String type) {
        List<InventorySoldOutDaysDO> beforeSoldOutList = soldOutDaysService.getBeforeSoldOutList(inventoryInfoIds, type);
        return beforeSoldOutList.stream().collect(Collectors.groupingBy(InventorySoldOutDaysDO::getInventoryInfoId));
    }

    @Override
    public void export(InventoryInfoQuery form, HttpServletResponse response) {
        form.setCurrent(1);
        form.setSize(-1);
        IPage<InventoryInfoDto> inventoryInfoDtoIPage = selectListByInfoIds(form);

        int total = (int) inventoryInfoDtoIPage.getTotal();
        List<List<Object>> inventoryInfoExportExcel = new ArrayList<>(total);
        List<ForeignRedundancyInfoExcel> foreignRedundancyInfo = new ArrayList<>(total);
        List<ShippingRedundancyInfoExcel> shippingRedundancyInfo = new ArrayList<>(total);
        List<FactoryRedundancyInfoExcel> factoryRedundancyInfo = new ArrayList<>(total);
        List<InventoryInfoDto> records = inventoryInfoDtoIPage.getRecords();
        List<String> dynamicHeadList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(records)) {
            List<String> infoIds = records.stream().map(InventoryInfoDto::getInventoryInfoId).toList();
            records.forEach(infoDto -> {
                Map<String, Integer> factoryInventory = infoDto.getFactoryInventory();
                Map<String, Integer> factoryInventoryCN = factoryInventory.entrySet().stream()
                        .collect(Collectors.toMap(
                                entry -> FactoryPlanInfoEnum.ofCode(entry.getKey()).getDesc(),
                                Map.Entry::getValue
                        ));
                infoDto.setFactoryInventory(factoryInventoryCN);

//                Map<String, String> leadTime = infoDto.getLeadTime();
//                Map<String, String> leadTimeCN = leadTime.entrySet().stream()
//                        .collect(Collectors.toMap(
//                                entry -> InventorySaleRuleEnum.ofCode(entry.getKey()).getDesc(),
//                                Map.Entry::getValue
//                        ));
//                infoDto.setLeadTime(leadTimeCN);
            });

            var foreignRedundantList = inventoryForeignRedundantInfoService.getForeignRedundantList(infoIds);
            Map<String, List<InventoryForeignRedundantBO>> foreignRedundantMap = foreignRedundantList.stream().collect(Collectors.groupingBy(InventoryForeignRedundantBO::getInventoryInfoId));

            var shippingRedundantList = inventoryForeignRedundantInfoService.getShippingRedundantListByInfoIds(infoIds);
            Map<String, List<InventoryShipRedundantInfoDto>> shippingRedundantMap = shippingRedundantList.stream().collect(Collectors.groupingBy(InventoryShipRedundantInfoDto::getInventoryInfoId));


            List<InventoryOnShippingRedundantBO> onShippingRedundantInfoList = inventoryWatchBoardService.getOnShippingRedundantInfoList(infoIds)
                    .stream()
                    .filter(item -> item.getShipmentCode() != null)
                    .toList();
            Map<String, List<InventoryOnShippingRedundantBO>> onShippingRedundantMap = onShippingRedundantInfoList.stream().collect(Collectors.groupingBy(InventoryOnShippingRedundantBO::getInventoryInfoId));

            List<InventoryFactoryRedundantInfoDto> factoryRedundantInfoList = inventoryFactoryRedundantInfoService.getFactoryRedundantInfoList(infoIds);
            Map<String, List<InventoryFactoryRedundantInfoDto>> factoryRedundantMap = factoryRedundantInfoList.stream().collect(Collectors.groupingBy(InventoryFactoryRedundantInfoDto::getInventoryInfoId));
            Map<String, List<InventorySoldOutDaysDO>> beforeSoldOutMap = getBeforeSoldOutList(infoIds, SOLD_OUT_DAYS);
            Map<String, List<InventorySoldOutDaysDO>> fullLinkBeforeSoldOutMap = getBeforeSoldOutList(infoIds, FULL_LINK_SOLD_OUT_DAYS);

            Map<String, Double> monthTargetSalesMap = records.stream()
                    .max(Comparator.comparing(c -> c.getMonthTargetSalesMap().size()))
                    .map(InventoryInfoDto::getMonthTargetSalesMap)
                    .get();
            dynamicHeadList.addAll(monthTargetSalesMap.keySet());

            // 冗余库存基本信息sheet页
            inventoryInfoExportExcel = getInventoryBaseInfoExportExcel(records, foreignRedundantMap, onShippingRedundantMap,
                    factoryRedundantMap, beforeSoldOutMap, fullLinkBeforeSoldOutMap, dynamicHeadList);

            // 海外仓冗余库存信息
            foreignRedundancyInfo = getForeignRedundancyInfo(records, foreignRedundantMap);

            // 在途冗余库存信息
            shippingRedundancyInfo = getShippingRedundancyInfo(records, shippingRedundantMap);

            // 工厂冗余库存信息
            factoryRedundancyInfo = getFactoryRedundancyInfo(records, factoryRedundantMap);


        }
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("库存明细_" + DateUtil.today(), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headWriteCellStyle.setShrinkToFit(true);
            headWriteCellStyle.setWrapped(false);
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            contentWriteCellStyle.setWrapped(true);
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

            WriteSheet writeSheet1 = EasyExcel.writerSheet(1, "sheet").head(getHeadList(dynamicHeadList))
                    .registerWriteHandler(new ExcelWidthStyleStrategy())
                    .registerWriteHandler(horizontalCellStyleStrategy).build();
            excelWriter.write(inventoryInfoExportExcel, writeSheet1);

            WriteSheet writeSheet2 = EasyExcel.writerSheet(2, "海外仓冗余详情").head(ForeignRedundancyInfoExcel.class)
                    .registerWriteHandler(new ExcelWidthStyleStrategy())
                    .registerWriteHandler(horizontalCellStyleStrategy).build();
            excelWriter.write(foreignRedundancyInfo, writeSheet2);

            WriteSheet writeSheet3 = EasyExcel.writerSheet(3, "在途冗余详情").head(ShippingRedundancyInfoExcel.class)
                    .registerWriteHandler(new ExcelWidthStyleStrategy())
                    .registerWriteHandler(horizontalCellStyleStrategy).build();
            excelWriter.write(shippingRedundancyInfo, writeSheet3);

            WriteSheet writeSheet4 = EasyExcel.writerSheet(4, "工厂冗余详情").head(FactoryRedundancyInfoExcel.class)
                    .registerWriteHandler(new ExcelWidthStyleStrategy())
                    .registerWriteHandler(horizontalCellStyleStrategy).build();
            excelWriter.write(factoryRedundancyInfo, writeSheet4);
        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    private List<List<String>> getHeadList(List<String> dynamicHeadList) {
        InventoryInfoExportDynamicHeadExcel headExcel = new InventoryInfoExportDynamicHeadExcel(dynamicHeadList);
        return headExcel.getDynamicHeadLit();
    }

    private List<List<Object>> getInventoryBaseInfoExportExcel(
            List<InventoryInfoDto> records, Map<String, List<InventoryForeignRedundantBO>> foreignRedundantMap,
            Map<String, List<InventoryOnShippingRedundantBO>> onShippingRedundantMap, Map<String, List<InventoryFactoryRedundantInfoDto>> factoryRedundantMap,
            Map<String, List<InventorySoldOutDaysDO>> beforeSoldOutList, Map<String, List<InventorySoldOutDaysDO>> fullLinkBeforeSoldOutList,
            List<String> dynamicHeadList
    ) {
        List<InventoryInfoExportExcel> inventoryInfoExportExcel = redundantInventoryAssembler.inventoryInfoDtoToExportExcel(records);
        List<List<Object>> list = new ArrayList<>();
        inventoryInfoExportExcel.forEach(k -> {
            StringBuilder operatorListBuilder = new StringBuilder();
            StringBuilder foreignDetailBuilder = new StringBuilder();
            StringBuilder onShippingRedundantDetailBuilder = new StringBuilder();
            StringBuilder factoryDetailBuilder = new StringBuilder();
            StringBuilder soldOutDetailBuilder = new StringBuilder();
            StringBuilder fullLinkSoldOutDetailBuilder = new StringBuilder();
            k.setBorrowingStrategy(k.getBorrowingStrategy() != null
                    ? BorrowingStrategyEnum.ofCode(Integer.valueOf(k.getBorrowingStrategy())).getDesc() : null);
            String operatorList = k.getOperatorList();
            if (StrUtil.isNotBlank(operatorList)) {
                String[] operators = operatorList.split(",");
                for (String operator : operators) {
                    operatorListBuilder.append(operator).append("\n");
                }
                if (!operatorListBuilder.isEmpty()) {
                    operatorListBuilder.setLength(operatorListBuilder.length() - 1);
                }
                k.setOperatorList(operatorListBuilder.toString());
            }

            // 海外仓冗余
            List<InventoryForeignRedundantBO> inventoryForeignRedundantBOS = foreignRedundantMap.get(k.getInventoryInfoId());
            if (CollectionUtil.isNotEmpty(inventoryForeignRedundantBOS)) {
                inventoryForeignRedundantBOS.forEach(i -> {
                    if (StrUtil.isNotBlank(k.getForeignRedundantInventoryDetail())) {
                        foreignDetailBuilder.append(k.getForeignRedundantInventoryDetail());
                    }
                    foreignDetailBuilder.append(i.getWarehouse())
                            .append(":")
                            .append(i.getRedundantNum().intValue())
                            .append("\n");
                });
                if (!foreignDetailBuilder.isEmpty()) {
                    foreignDetailBuilder.setLength(foreignDetailBuilder.length() - 1);
                }
                k.setForeignRedundantInventoryDetail(foreignDetailBuilder.toString());
            }

            // 海外仓在途冗余
            List<InventoryOnShippingRedundantBO> onShippingRedundantBOS = onShippingRedundantMap.get(k.getInventoryInfoId());
            if (CollectionUtil.isNotEmpty(onShippingRedundantBOS)) {
                onShippingRedundantBOS.forEach(i -> {
                    if (StrUtil.isNotBlank(k.getOnShippingRedundantInventoryDetail())) {
                        onShippingRedundantDetailBuilder.append(k.getOnShippingRedundantInventoryDetail());
                    }
                    onShippingRedundantDetailBuilder
                            .append(i.getShipmentCode())
                            .append("，")
                            .append(i.getWarehouse())
                            .append("，")
                            .append(i.getEnableUsingDate())
                            .append("，")
                            .append(i.getRedundantNum())
                            .append("\n");
                });
                if (!onShippingRedundantDetailBuilder.isEmpty()) {
                    onShippingRedundantDetailBuilder.setLength(onShippingRedundantDetailBuilder.length() - 1);
                }
                k.setOnShippingRedundantInventoryDetail(onShippingRedundantDetailBuilder.toString());
            }
            // 本地仓冗余
            List<InventoryFactoryRedundantInfoDto> inventoryFactoryRedundantInfoDtos = factoryRedundantMap.get(k.getInventoryInfoId());
            if (CollectionUtil.isNotEmpty(inventoryFactoryRedundantInfoDtos)) {
                inventoryFactoryRedundantInfoDtos.forEach(i -> {
                    if (StrUtil.isNotBlank(k.getFactoryRedundantInventoryDetail())) {
                        factoryDetailBuilder.append(k.getFactoryRedundantInventoryDetail());
                    }
                    factoryDetailBuilder.append(i.getContractCode())
                            .append("，")
                            .append(i.getDestinationSku())
                            .append("，")
                            .append(i.getFactoryFinishedDate())
                            .append("，")
                            .append(i.getRedundantNum().intValue())
                            .append("\n");
                });
                if (!factoryDetailBuilder.isEmpty()) {
                    factoryDetailBuilder.setLength(factoryDetailBuilder.length() - 1);
                }
                k.setFactoryRedundantInventoryDetail(factoryDetailBuilder.toString());
            }

            // 断货天数详情
            List<InventorySoldOutDaysDO> soldOutDateBeforeTheoryDtos = beforeSoldOutList.get(k.getInventoryInfoId());
            if (CollectionUtil.isNotEmpty(soldOutDateBeforeTheoryDtos)) {
                soldOutDateBeforeTheoryDtos.forEach(i -> {
                    if (StrUtil.isNotBlank(k.getDaysBeforeSoldOutDetail())) {
                        soldOutDetailBuilder.append(k.getDaysBeforeSoldOutDetail());
                    }
                    soldOutDetailBuilder.append(i.getDaysGap())
                            .append("\n");
                });
                if (!soldOutDetailBuilder.isEmpty()) {
                    soldOutDetailBuilder.setLength(soldOutDetailBuilder.length() - 1);
                }
                k.setDaysBeforeSoldOutDetail(soldOutDetailBuilder.toString());
            }

            // 断货天数详情
            List<InventorySoldOutDaysDO> fullLinkSoldOutDateBeforeTheoryDtos = fullLinkBeforeSoldOutList.get(k.getInventoryInfoId());
            if (CollectionUtil.isNotEmpty(fullLinkSoldOutDateBeforeTheoryDtos)) {
                fullLinkSoldOutDateBeforeTheoryDtos.forEach(i -> {
                    if (StrUtil.isNotBlank(k.getFullLinkDaysBeforeSoldOutDetail())) {
                        fullLinkSoldOutDetailBuilder.append(k.getFullLinkDaysBeforeSoldOutDetail());
                    }
                    fullLinkSoldOutDetailBuilder.append(i.getDaysGap())
                            .append("\n");
                });
                if (!fullLinkSoldOutDetailBuilder.isEmpty()) {
                    fullLinkSoldOutDetailBuilder.setLength(fullLinkSoldOutDetailBuilder.length() - 1);
                }
                k.setFullLinkDaysBeforeSoldOutDetail(fullLinkSoldOutDetailBuilder.toString());
            }

            List<Object> tempList = new ArrayList<>();
            tempList.add(k.getVirtualSku());
            tempList.add(k.getSelfSku());
            tempList.add(k.getProductName());
            tempList.add(k.getChannel());
            tempList.add(k.getProductStatus());
            tempList.add(k.getSubType());
            tempList.add(k.getProductType());
            tempList.add(k.getBorrowingStrategy());
            tempList.add(k.getOperatorList());

            for (var head : dynamicHeadList) {
                tempList.add(k.getMonthTargetSalesMap().getOrDefault(head, 0D));
            }
            // 获取7、14、30、目标日销、实际日销、子体达成率
            tempList.add(k.getSevenDaySales() != null ? Double.parseDouble(k.getSevenDaySales()) : null);
            tempList.add(k.getFourteenDaySales() != null ? Double.parseDouble(k.getFourteenDaySales()) : null);
            tempList.add(k.getThirtyDaySales() != null ? Double.parseDouble(k.getThirtyDaySales()) : null);
            tempList.add(k.getActualDailySales() != null ? Double.parseDouble(k.getActualDailySales()) : null);
            tempList.add(k.getTargetSalesNum() != null ? Double.parseDouble(k.getTargetSalesNum()) : null);
            tempList.add(k.getSubEntityRate());

            // 海外仓、在途、本地仓库存情况
            tempList.add(k.getFactoryInventory());
            tempList.add(k.getForeignInTransit());
            tempList.add(k.getForeignInventory());

            // 海外仓售罄情况
            tempList.add(k.getForeignTheoreticalSoldOutDate());
            tempList.add(k.getLackNum());
            tempList.add(k.getDaysBeforeSoldOut());
            tempList.add(k.getDaysBeforeSoldOutDetail());

            // 全链路售罄情况
            tempList.add(k.getFullLinkForeignTheoreticalSoldOutDate());
            tempList.add(k.getFullLinkLackNum());
            tempList.add(k.getFullLinkDaysBeforeSoldOut());
            tempList.add(k.getFullLinkDaysBeforeSoldOutDetail());

            // 冗余情况
            tempList.add(k.getForeignRedundantInventory());
            tempList.add(k.getForeignRedundantInventoryDetail());
            tempList.add(k.getOnShippingRedundantInventory());
            tempList.add(k.getOnShippingRedundantInventoryDetail());
            tempList.add(k.getFactoryRedundantInventory());
            tempList.add(k.getFactoryRedundantInventoryDetail());

            tempList.add(k.getLeadTime());
            tempList.add(k.getUpdateDate());
            list.add(tempList);
        });
        return list;
    }

    private List<ForeignRedundancyInfoExcel> getForeignRedundancyInfo(List<InventoryInfoDto> records,
                                                                      Map<String, List<InventoryForeignRedundantBO>> foreignRedundantMap) {
        List<ForeignRedundancyInfoExcel> foreignRedundancyInfoExcelList = new ArrayList<>(records.size());
        for (var inventoryInfo : records) {
            List<InventoryForeignRedundantBO> foreignRedundancyInfoList = foreignRedundantMap.getOrDefault(inventoryInfo.getInventoryInfoId(), new ArrayList<>());

            for (var bo : foreignRedundancyInfoList) {
                foreignRedundancyInfoExcelList.add(ForeignRedundancyInfoExcel.builder()
                        .virtualSku(inventoryInfo.getVirtualSku())
                        .selfSku(inventoryInfo.getSelfSku())
                        .productName(inventoryInfo.getProductName())
                        .channel(inventoryInfo.getChannel())
                        .productStatus(Optional.ofNullable(VirtualProductStatusEnum.ofCode(inventoryInfo.getProductStatus()))
                                .map(VirtualProductStatusEnum::getDesc)
                                .orElse(""))
                        .subType(Optional.ofNullable(VirtualSubTypeEnum.ofCode(inventoryInfo.getSubType()))
                                .map(VirtualSubTypeEnum::getDesc)
                                .orElse(""))
                        .productType(Optional.ofNullable(VirtualProductTypeEnum.ofCode(inventoryInfo.getProductType()))
                                .map(VirtualProductTypeEnum::getDesc)
                                .orElse(""))
                        .warehouse(bo.getWarehouse())
                        .redundancy(bo.getRedundantNum())
                        .borrowingStrategy(inventoryInfo.getBorrowingStrategy() != null
                                ? BorrowingStrategyEnum.ofCode(inventoryInfo.getBorrowingStrategy()).getDesc() : null)
                        .build());
            }
        }
        return foreignRedundancyInfoExcelList;
    }

    private List<ShippingRedundancyInfoExcel> getShippingRedundancyInfo(List<InventoryInfoDto> records,
                                                                        Map<String, List<InventoryShipRedundantInfoDto>> shippingRedundantMap) {
        List<ShippingRedundancyInfoExcel> foreignRedundancyInfoExcelList = new ArrayList<>(records.size());
        for (var inventoryInfo : records) {
            List<InventoryShipRedundantInfoDto> foreignRedundancyInfoList = shippingRedundantMap.getOrDefault(inventoryInfo.getInventoryInfoId(), new ArrayList<>());

            for (var bo : foreignRedundancyInfoList) {
                foreignRedundancyInfoExcelList.add(ShippingRedundancyInfoExcel.builder()
                        .virtualSku(inventoryInfo.getVirtualSku())
                        .selfSku(inventoryInfo.getSelfSku())
                        .productName(inventoryInfo.getProductName())
                        .channel(inventoryInfo.getChannel())
                        .productStatus(Optional.ofNullable(VirtualProductStatusEnum.ofCode(inventoryInfo.getProductStatus()))
                                .map(VirtualProductStatusEnum::getDesc)
                                .orElse(""))
                        .subType(Optional.ofNullable(VirtualSubTypeEnum.ofCode(inventoryInfo.getSubType()))
                                .map(VirtualSubTypeEnum::getDesc)
                                .orElse(""))
                        .productType(Optional.ofNullable(VirtualProductTypeEnum.ofCode(inventoryInfo.getProductType()))
                                .map(VirtualProductTypeEnum::getDesc)
                                .orElse(""))
                        .warehouse(bo.getWarehouse())
                        .shipmentCode(bo.getShipmentCode())
                        .redundantNum(bo.getRedundantNum())
                        .enableUsingDate(bo.getEnableUsingDate().format(DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_HYPHEN)))
                        .startShippingDate(bo.getStartShippingDate() != null ? bo.getStartShippingDate().format(DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_HYPHEN)) : null)
                        .borrowingStrategy(inventoryInfo.getBorrowingStrategy() != null
                                ? BorrowingStrategyEnum.ofCode(inventoryInfo.getBorrowingStrategy()).getDesc() : null)
                        .build());
            }
        }
        return foreignRedundancyInfoExcelList;
    }

    private List<FactoryRedundancyInfoExcel> getFactoryRedundancyInfo(List<InventoryInfoDto> records,
                                                                      Map<String, List<InventoryFactoryRedundantInfoDto>> factoryRedundantMap) {

        List<FactoryRedundancyInfoExcel> factoryRedundancyInfoExcelList = new ArrayList<>(records.size());
        for (var inventoryInfo : records) {
            List<InventoryFactoryRedundantInfoDto> factoryRedundancyInfoList = factoryRedundantMap.getOrDefault(inventoryInfo.getInventoryInfoId(), new ArrayList<>());
            for (var bo : factoryRedundancyInfoList) {
                factoryRedundancyInfoExcelList.add(FactoryRedundancyInfoExcel.builder()
                        .virtualSku(inventoryInfo.getVirtualSku())
                        .selfSku(inventoryInfo.getSelfSku())
                        .productName(inventoryInfo.getProductName())
                        .channel(inventoryInfo.getChannel())
                        .productStatus(Optional.ofNullable(VirtualProductStatusEnum.ofCode(inventoryInfo.getProductStatus()))
                                .map(VirtualProductStatusEnum::getDesc)
                                .orElse(""))
                        .subType(Optional.ofNullable(VirtualSubTypeEnum.ofCode(inventoryInfo.getSubType()))
                                .map(VirtualSubTypeEnum::getDesc)
                                .orElse(""))
                        .productType(Optional.ofNullable(VirtualProductTypeEnum.ofCode(inventoryInfo.getProductType()))
                                .map(VirtualProductTypeEnum::getDesc)
                                .orElse(""))
                        .borrowingStrategy(inventoryInfo.getBorrowingStrategy() != null
                                ? BorrowingStrategyEnum.ofCode(inventoryInfo.getBorrowingStrategy()).getDesc() : null)
                        .contractCode(bo.getContractCode())
                        .virtualSku(bo.getDestinationSku())
                        .factoryFinishedDate(bo.getFactoryFinishedDate())
                        .redundancy(bo.getRedundantNum())
                        .build());
            }
        }
        return factoryRedundancyInfoExcelList;
    }

    @Override
    public LeadTimeDto getLeadTime(String inventoryInfoId) {
        List<InventorySaleRulesDO> saleRuleList = inventorySaleRulesService.getInventorySaleByInfoIds(Collections.singletonList(inventoryInfoId));
        Map<String, InventorySaleRulesDO> saleRuleMap = saleRuleList.stream().collect(Collectors.toMap(InventorySaleRulesDO::getInventoryInfoId, i -> i));

        InventorySaleRulesDO inventorySaleRulesDO = saleRuleMap.get(inventoryInfoId);

        // 加急补货到货天数
        int urgentHeadShipDate;
        Map<String, Double> saleRatioMap = JSON.parseObject(inventorySaleRulesDO.getSaleRatio(), new TypeReference<>() {
        });
        if (Objects.equals(inventorySaleRulesDO.getUrgentShipDateEdited(), UrgentCircleEditedEnum.CHANGED.getCode())) {
            urgentHeadShipDate = inventorySaleRulesDO.getUrgentShipDate();
        } else {
            urgentHeadShipDate = warehouseService.getMinHeadShipDate(saleRatioMap);
        }
        String warehouse = warehouseService.getMinHeadShipDateWarehouse(saleRatioMap);

        Integer minValue = getMinHeadShipping(inventorySaleRulesDO);
        return LeadTimeDto.builder()
                .saleRuleId(inventorySaleRulesDO.getId())
                .purchaseDays(inventorySaleRulesDO.getPurchaseDays())
                .transitDays(inventorySaleRulesDO.getTransitDays())
                .normalPurchaseCircle(inventorySaleRulesDO.getNormalProduceDays())
                .urgentPurchaseCircle(inventorySaleRulesDO.getUrgentProduceDays())
                .urgentHeadShippingDate(urgentHeadShipDate)
                .headShippingDate(minValue)
                .urgentPurchaseWarehouse(warehouse)
                .build();
    }

    @Override
    public boolean updateUrgentProduceDays(LeadTimeCommand leadTimeCommand) {
        InventorySaleRulesDO inventorySaleRulesDO = InventorySaleRulesDO.builder()
                .id(leadTimeCommand.getSaleRuleId())
                .build();
        InventorySaleRulesDO inventorySale = inventorySaleRulesService.getInventorySaleById(inventorySaleRulesDO.getId());
        String inventoryInfoId = inventorySale.getInventoryInfoId();
        if (leadTimeCommand.getIsBackTracking() != null && leadTimeCommand.getIsBackTracking()) {
            // 回溯至该虚拟商品的最新生产交期，而不是快照的生产交期
            RedundantInventoryDO redundantInventory = redundantInventoryRepository.getById(inventoryInfoId);
            SelfAndVirtualBO selfProductDO = selfProductRepositoryImpl.getSelfProductByVirtualId(redundantInventory.getVirtualSkuId());
            ProduceDaysDp produceDaysDp = ProduceDaysDp.initProduceDaysDp(selfProductDO.getPurchaseDate());
            int produceDay = produceDaysDp.getProduceDay(redundantInventory.getCalFinishedDate().plusDays(1));
            inventorySaleRulesDO.setUrgentProduceDays(produceDay);
            inventorySaleRulesDO.setUrgentProduceDaysEdited(UrgentCircleEditedEnum.UNCHANGED.getCode());
        } else {
            inventorySaleRulesDO.setUrgentProduceDays(leadTimeCommand.getPurchaseCircle());
            inventorySaleRulesDO.setUrgentProduceDaysEdited(UrgentCircleEditedEnum.CHANGED.getCode());
        }

        inventorySaleRulesService.updateInventorySaleRules(inventorySaleRulesDO);
        redundantInventoryRepository.updateById(RedundantInventoryDO.builder()
                .id(inventoryInfoId)
                .build());

        inventorySale = inventorySaleRulesService.getInventorySaleById(inventorySaleRulesDO.getId());
        reCalUrgentPurchase(inventorySale, inventoryInfoId);
        return true;
    }

    @Override
    public boolean updateUrgentHeadShipDate(UrgentHeadShipDateForm urgentForm) {
        InventorySaleRulesDO inventorySale = inventorySaleRulesService.getInventorySaleById(urgentForm.getSaleRuleId());
        if (inventorySale == null) {
            throw new IllegalArgumentException("找不到该冗余库存相关数据");
        }

        InventorySaleRulesDO pendingToUpdate = InventorySaleRulesDO.builder()
                .id(urgentForm.getSaleRuleId())
                .build();
        if (urgentForm.getIsBackTracking() != null && urgentForm.getIsBackTracking()) {
            pendingToUpdate.setUrgentShipDateEdited(UrgentCircleEditedEnum.UNCHANGED.getCode());
        } else {
            pendingToUpdate.setUrgentShipDate(urgentForm.getUrgentHeadShipDate());
            pendingToUpdate.setUrgentShipDateEdited(UrgentCircleEditedEnum.CHANGED.getCode());
        }
        inventorySaleRulesService.updateInventorySaleRules(pendingToUpdate);
        redundantInventoryRepository.updateById(RedundantInventoryDO.builder()
                .id(inventorySale.getId())
                .build());

        inventorySale = inventorySaleRulesService.getInventorySaleById(inventorySale.getId());
        reCalUrgentPurchase(inventorySale, inventorySale.getInventoryInfoId());
        return true;
    }

    private void reCalUrgentPurchase(InventorySaleRulesDO inventorySale, String inventoryInfoId) {
        // 获取库存信息
        RedundantInventoryDO inventoryInfoDO = redundantInventoryRepository.getById(inventoryInfoId);

        // 获取目标日销
        Map<String, BigDecimal> targetSalesMap = JSON.parseObject(inventorySale.getSaleDestination(), new TypeReference<>() {
        });

        // 获取正常发货试算结果
        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        NormalDeliveryWatchBoardVo watchBoardVo = getNormalShippingCalculation(inventoryInfoId, senboWarehouseList);
        DeliveryCalResultDto deliveryCalResultDto = watchBoardVo.getDeliveryCalResultDto();
        // 发送mq
        synchronized (lock) {
            rocketMQTemplate.convertAndSend(URGENT_PURCHASE_TOPIC, UrgentPurchaseMQForm.builder()
                    .snapShotId(inventoryInfoDO.getSnapshotId())
                    .inventoryInfoId(inventoryInfoId)
                    .targetSalesMap(targetSalesMap)
                    .calFinishedDate(inventoryInfoDO.getCalFinishedDate())
                    .salesRules(inventorySale)
                    .normalDeliveryResultDto(deliveryCalResultDto)
                    .build());
        }
    }

    private IPage<InventoryInfoDto> selectListByInfoIds(InventoryInfoQuery form) {
        if (StrUtil.isNotBlank(form.getCategoryId())) {
            RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
            ResultDTO resultDTO = restTemplateUtil.get(PRODUCTS_SELF_CATEGORY_LEAF_TREE_URL, ResultDTO.class);
            Map<String, ProductCategoryDTO> categoryDTOMap = JSON.to(HashMap.class, resultDTO.getData());

            for (Map.Entry<String, ProductCategoryDTO> category : categoryDTOMap.entrySet()) {
                ProductCategoryDTO value = JSON.to(ProductCategoryDTO.class, category.getValue());
                if (value.getId().toString().equals(form.getCategoryId())) {
                    form.setCategoryId(category.getKey());
                }
            }
        }
        IPage<InventoryFactoryInfoA> inventoryFactoryInfoAIPage = redundantInventoryRepository.pageList(form);
        if (CollectionUtil.isEmpty(inventoryFactoryInfoAIPage.getRecords())) {
            return new Page<>(form.getCurrent(), form.getSize(), inventoryFactoryInfoAIPage.getTotal());
        }
        List<String> ids = inventoryFactoryInfoAIPage.getRecords().stream().map(InventoryFactoryInfoA::getInventoryInfoId).toList();

        //工厂库存
        List<InventoryFactoryPlanInfoA> planInfoList = inventoryFactoryPlanInfoService.getPlanInfoListByInfoIds(ids);
        Map<String, List<InventoryFactoryPlanInfoA>> planInfoMap = planInfoList.stream().collect(Collectors.groupingBy(InventoryFactoryPlanInfoA::getInventoryInfoId));

        //海外库存
        List<InventoryForeignStoreDO> foreignStoreList = inventoryForeignStoreService.getForeignStoreListByInfoIds(ids);
        Map<String, List<InventoryForeignStoreDO>> foreignStoreMap = foreignStoreList.stream().collect(Collectors.groupingBy(InventoryForeignStoreDO::getInventoryInfoId));

        IPage<InventoryInfoDto> inventoryInfoDtoIPage = new Page<>(form.getCurrent(), form.getSize());
        List<InventoryInfoDto> inventoryInfoDtoList = new ArrayList<>();

        //工厂库存,海外仓在途，海外仓库存
        List<InventorySaleRulesDO> saleRuleList = inventorySaleRulesService.getInventorySaleByInfoIds(ids);
        Map<String, InventorySaleRulesDO> saleRuleMap = saleRuleList.stream().collect(Collectors.toMap(InventorySaleRulesDO::getInventoryInfoId, i -> i));

        String summarize = "合计";
        Comparator<String> customComparator = (s1, s2) -> {
            if (s1.equals(summarize) && !s2.equals(summarize)) return -1;
            if (s1.equals(summarize)) return 0;
            if (s2.equals(summarize)) return 1;
            return s1.compareTo(s2);
        };

        //运营
        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> userNameNickNameMap = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();

        var lackNumMap = soldOutDaysService.sumLackNumByInventoryIdList(ids, SOLD_OUT_DAYS);
        var fullLinkLackNumMap = soldOutDaysService.sumLackNumByInventoryIdList(ids, FULL_LINK_SOLD_OUT_DAYS);
        for (InventoryFactoryInfoA infoA : inventoryFactoryInfoAIPage.getRecords()) {
            Map<String, Integer> factoryInventory = new HashMap<>();
            Map<String, Integer> foreignInTransit = new TreeMap<>(customComparator);
            Map<String, Integer> foreignInventory = new TreeMap<>(customComparator);
            String inventoryInfoId = infoA.getInventoryInfoId();
            int producedNum = 0;
            int inProduction = 0;
            List<InventoryFactoryPlanInfoA> inventoryFactoryPlanInfoAS = planInfoMap.get(inventoryInfoId);
            if (!CollectionUtil.isEmpty(inventoryFactoryPlanInfoAS)) {
                for (InventoryFactoryPlanInfoA planInfo : planInfoMap.get(inventoryInfoId)) {
                    if (DateUtil.compare(infoA.getCalFinishedDate(), planInfo.getFactoryFinishedDate()) >= 0) {
                        producedNum += planInfo.getStoreNum();
                    } else {
                        inProduction += planInfo.getStoreNum();
                    }
                }
                factoryInventory.put(PRODUCT.getCode(), producedNum);
                factoryInventory.put(IN_PRODUCTION.getCode(), inProduction);
            } else {
                factoryInventory.put(PRODUCT.getCode(), 0);
                factoryInventory.put(IN_PRODUCTION.getCode(), 0);
            }

            List<InventoryForeignStoreDO> inventoryForeignStoreDOS = foreignStoreMap.get(inventoryInfoId);
            if (CollectionUtil.isEmpty(inventoryForeignStoreDOS)) {
                foreignInTransit.put(summarize, 0);
                foreignInventory.put(summarize, 0);
            } else {
                foreignStoreMap.get(inventoryInfoId).forEach((k) -> {
                    Integer foreignInventoryNum = 0;
                    Integer foreignInTransitNum = 0;
                    if (DateUtil.compare(k.getEnableUsingDate(), infoA.getCalFinishedDate()) <= 0) {
                        foreignInventoryNum = k.getInventoryNum();
                        Integer orDefault = foreignInventory.getOrDefault(k.getWarehouse(), 0);
                        foreignInventory.put(k.getWarehouse(), orDefault + foreignInventoryNum);
                    } else {
                        foreignInTransitNum = k.getInventoryNum();
                        Integer orDefault = foreignInTransit.getOrDefault(k.getWarehouse(), 0);
                        foreignInTransit.put(k.getWarehouse(), orDefault + foreignInTransitNum);
                    }
                    foreignInventory.put(summarize, foreignInventory.getOrDefault(summarize, 0) + foreignInventoryNum);
                    foreignInTransit.put(summarize, foreignInTransit.getOrDefault(summarize, 0) + foreignInTransitNum);
                });
            }

            InventorySaleRulesDO salesRules = saleRuleMap.get(inventoryInfoId);
            Map<String, Double> targetSalesMap = JSON.parseObject(salesRules.getSaleDestination(), new TypeReference<>() {
            });
            Map<String, Double> monthTargetSalesMap = targetSalesMap.entrySet().stream()
                    .collect(Collectors.toMap(
                                    (Map.Entry<String, Double> m) -> YearMonth.parse(m.getKey(), formatter).format(slashYearMonthFormatter),
                                    Map.Entry::getValue,
                                    (o1, o2) -> BigDecimal.valueOf(o1 + o2).setScale(3, HALF_UP).doubleValue(),
                                    TreeMap::new
                            )
                    );

            String operator = infoA.getOperator();
            String operatorNames = "";
            if (StrUtil.isNotBlank(operator)) {
                // 使用String.split()方法的新重载,直接返回Stream
                operatorNames = Arrays.stream(operator.split(",", 0))
                        .map(op -> StrUtil.isNotBlank(userNameNickNameMap.get(op)) ? userNameNickNameMap.get(op) : op)
                        .collect(Collectors.joining(","));
            }

            infoA.setChannel(channelIdNameMap.getOrDefault(infoA.getChannel(), infoA.getChannel()));

            double totalLackNum = lackNumMap.getOrDefault(inventoryInfoId, 0D);
            double fullLinkTotalLackNum = fullLinkLackNumMap.getOrDefault(inventoryInfoId, 0D);

            // 若该商品是升级款，找到被升级款的虚拟sku
            String formerVirtualSku = "";
            if (StrUtil.isNotBlank(infoA.getUpgradeType()) && infoA.getUpgradeType().equals(UpgradeTypeEnum.UPGRADE.getCode())) {
                formerVirtualSku = virtualProductRepositoryImpl.getVirtualSkuByVirtualId(infoA.getUpgradeVirtualId());
            }

            // 可干预时间
            InterventionalTimeDto interventionalTimeDto = interventionalTimeService.calculateInterventionalTime(
                    DateUtils.convertToLocalDate(infoA.getCalFinishedDate()), salesRules);

            inventoryInfoDtoList.add(
                    InventoryInfoDto.builder()
                            .inventoryInfoId(inventoryInfoId)
                            .image(infoA.getImage())
                            .virtualSku(infoA.getVirtualSku())
                            .selfSku(infoA.getSelfSku())
                            .selfSkuId(infoA.getSelfSkuId())
                            .productName(infoA.getProductName())
                            .factoryInventory(factoryInventory)
                            .foreignInTransit(foreignInTransit)
                            .foreignInventory(foreignInventory)
                            // 售罄时间
                            .foreignTheoreticalSoldOutDate(infoA.getForeignTheoreticalSoldOutDate() == null ? null :
                                    LocalDateTimeUtil.format(LocalDateTimeUtil.of(infoA.getForeignTheoreticalSoldOutDate()).toLocalDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN))
                            .lackNum(totalLackNum)
                            .daysBeforeSoldOut(infoA.getDaysBeforeSoldOut())
                            // 全链路售罄时间
                            .fullLinkForeignTheoreticalSoldOutDate(infoA.getFullLinkTheoreticalSoldOutDate() == null ? null :
                                    LocalDateTimeUtil.format(LocalDateTimeUtil.of(infoA.getFullLinkTheoreticalSoldOutDate()).toLocalDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN))
                            .fullLinkLackNum(fullLinkTotalLackNum)
                            .fullLinkDaysBeforeSoldOut(infoA.getFullLinkDaysBeforeSoldOut())
                            .foreignRedundantInventory(infoA.getForeignRedundantInventory())
                            .onShippingRedundantInventory(infoA.getOnShippingRedundantInventory())
                            .factoryRedundantInventory(infoA.getFactoryRedundantInventory())
                            .leadTime(interventionalTimeDto)
                            .updateDate(infoA.getUpdateDate())
                            .calFinishedDate(infoA.getCalFinishedDate())
                            .channel(infoA.getChannel())
                            .subType(infoA.getSubType())
                            .productType(infoA.getProductType())
                            .productStatus(infoA.getProductStatus())
                            .operatorList(operatorNames)
                            .formerSku(formerVirtualSku)
                            .monthTargetSalesMap(monthTargetSalesMap)
                            .sevenDaySales(infoA.getSevenDaySales() != null ? BigDecimal.valueOf(infoA.getSevenDaySales()).stripTrailingZeros().toPlainString() : null)
                            .fourteenDaySales(infoA.getFourteenDaySales() != null ? BigDecimal.valueOf(infoA.getFourteenDaySales()).stripTrailingZeros().toPlainString() : null)
                            .thirtyDaySales(infoA.getThirtyDaySales() != null ? BigDecimal.valueOf(infoA.getThirtyDaySales()).stripTrailingZeros().toPlainString() : null)
                            .actualDailySales(infoA.getActualDailySales() != null ? BigDecimal.valueOf(infoA.getActualDailySales()).stripTrailingZeros().toPlainString() : null)
                            .targetSalesNum(infoA.getTargetSalesNum() != null ? BigDecimal.valueOf(infoA.getTargetSalesNum()).stripTrailingZeros().toPlainString() : null)
                            .subEntityRate(formatPercent(infoA.getSubEntityRate()))
                            .fullLinkSalableDays(infoA.getFullLinkSalableDays() != null ? String.valueOf(infoA.getFullLinkSalableDays()) : null)
                            .salableDays(infoA.getSalableDays() != null ? String.valueOf(infoA.getSalableDays()) : null)
                            .borrowingStrategy(infoA.getBorrowingStrategy())
                            .build()
            );
        }
        inventoryInfoDtoIPage.setRecords(inventoryInfoDtoList);
        inventoryInfoDtoIPage.setTotal(inventoryFactoryInfoAIPage.getTotal());
        return inventoryInfoDtoIPage;
    }

    public static String formatPercent(Double number) {
        if (number == null || number == 0) {
            return "0%";
        }
        return String.format("%.2f%%", number * 100);
    }

    private Integer getMinHeadShipping(InventorySaleRulesDO saleRule) {
        Map<String, Integer> headShippingDate = JSON.parseObject(saleRule.getHeadShippingDate(), new TypeReference<>() {
        });
        Map<String, Double> saleRatio = JSON.parseObject(saleRule.getSaleRatio(), new TypeReference<>() {
        });
        int minValue = Integer.MAX_VALUE;

        for (Map.Entry<String, Double> entry : saleRatio.entrySet()) {
            String key = entry.getKey();
            double value = entry.getValue();

            if (value > 0 && headShippingDate.containsKey(key)) {
                int shippingDateValue = headShippingDate.get(key);
                if (shippingDateValue < minValue) {
                    minValue = shippingDateValue;
                }
            }
        }
        return minValue;
    }

    @Override
    public String selectFileInfo(String filePath) {
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        String format = String.format(FILE_SYSTEM_MISSION_CENTER_MISSION_GET_URL + "?url=%s", REDUNDANT_INVENTORY_FILE_PATH + filePath);
        ResultDTO<String> resultDTO = restTemplateUtils.get(format, ResultDTO.class);
        return Optional.ofNullable(resultDTO).orElse(ResultDTO.success("")).getData();
    }

    @Override
    public InventoryFactoryAndForeignInfoDto getFactoryRedundantInfo(List<String> selfSkuIdList) {
        InventoryInfoQuery form = new InventoryInfoQuery();
        form.setCurrent(1);
        form.setSize(-1);
        form.setSelfSkuIdList(selfSkuIdList);
        IPage<InventoryInfoDto> inventoryInfoDtoIPage = selectListByInfoIds(form);
        List<InventoryInfoDto> records = inventoryInfoDtoIPage.getRecords();
        Map<String, List<InventoryInfoDto>> inventoryInfoSelfIdMap = records.stream().collect(Collectors.groupingBy(InventoryInfoDto::getSelfSkuId));
        List<String> inventoryInfoIds = records.stream().map(InventoryInfoDto::getInventoryInfoId).toList();
        if (CollectionUtil.isEmpty(inventoryInfoIds)) {
            return InventoryFactoryAndForeignInfoDto.builder().factoryAndForeignInfoMap(new HashMap<>()).build();
        }
        var foreignRedundantList = inventoryForeignRedundantInfoService.getForeignRedundantList(inventoryInfoIds);
        List<InventoryForeignRedundantInfoDto> inventoryForeignRedundantInfoDto = redundantInventoryAssembler.foreignRedundantBOToDto(foreignRedundantList);
        List<InventoryFactoryRedundantInfoDto> factoryRedundantInfoList = inventoryFactoryRedundantInfoService.getFactoryRedundantInfoList(inventoryInfoIds);

        List<InventoryOnShippingRedundantBO> onShippingRedundantInfoList = inventoryWatchBoardService.getOnShippingRedundantInfoList(inventoryInfoIds)
                .stream()
                .filter(item -> item.getShipmentCode() != null)
                .toList();


        Set<String> collect = Stream.of(
                inventoryForeignRedundantInfoDto.stream().map(InventoryForeignRedundantInfoDto::getInventoryInfoId),
                onShippingRedundantInfoList.stream().map(InventoryOnShippingRedundantBO::getInventoryInfoId),
                factoryRedundantInfoList.stream().map(InventoryFactoryRedundantInfoDto::getInventoryInfoId)
        ).flatMap(Function.identity()).collect(Collectors.toSet());
        List<InventoryFactoryInfoA> redundantInventoryDOS = redundantInventoryRepository.selectVirtualByInfoIds(collect);
        Map<String, String> infoIdWithVirtual = redundantInventoryDOS.stream().collect(Collectors.toMap(InventoryFactoryInfoA::getInventoryInfoId, InventoryFactoryInfoA::getVirtualSku));
        Map<String, String> channel = redundantInventoryDOS.stream().collect(Collectors.toMap(InventoryFactoryInfoA::getInventoryInfoId, InventoryFactoryInfoA::getChannel));

        // 先将 foreignRedundantList 按 inventoryInfoId 分组
        Map<String, List<InventoryForeignRedundantInfoDto>> foreignMap = inventoryForeignRedundantInfoDto.stream()
                .collect(Collectors.groupingBy(InventoryForeignRedundantInfoDto::getInventoryInfoId));

        // 冗余在途
        Map<String, List<InventoryOnShippingRedundantBO>> onShippingRedundantMap = onShippingRedundantInfoList.stream()
                .collect(Collectors.groupingBy(InventoryOnShippingRedundantBO::getInventoryInfoId));

        // 先将 factoryRedundantInfoList 按 inventoryInfoId 分组
        Map<String, List<InventoryFactoryRedundantInfoDto>> factoryMap = factoryRedundantInfoList.stream()
                .collect(Collectors.groupingBy(InventoryFactoryRedundantInfoDto::getInventoryInfoId));

        // 合并两个 map 到 resultMap 中
        Map<String, FactoryAndForeignInfoMapDto> resultMap = new HashMap<>();

        inventoryInfoSelfIdMap.forEach((selfSkuId, inventoryInfoList) -> {
            FactoryAndForeignInfoMapDto dto = resultMap.computeIfAbsent(selfSkuId, k -> new FactoryAndForeignInfoMapDto());
            List<InventoryForeignRedundantInfoDto> foreignInfo = new ArrayList<>();
            List<InventoryOnShippingRedundantBO> onShippingInfo = new ArrayList<>();
            List<InventoryFactoryRedundantInfoDto> factoryInfo = new ArrayList<>();
            Map<String, String> inventoryInfoByVirtual = new HashMap<>();
            inventoryInfoList.forEach(inventoryInfo -> {
                String inventoryInfoId = inventoryInfo.getInventoryInfoId();
                List<InventoryFactoryRedundantInfoDto> inventoryFactoryPlanInfoAS = factoryMap.get(inventoryInfoId);
                if (!CollectionUtil.isEmpty(inventoryFactoryPlanInfoAS)) {
                    for (InventoryFactoryRedundantInfoDto planInfo : inventoryFactoryPlanInfoAS) {
                        DateTime parse = DateUtil.parse(planInfo.getFactoryFinishedDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN);

                        if (DateUtil.compare(inventoryInfo.getCalFinishedDate(), parse) >= 0) {
                            planInfo.setIsProduct(PRODUCT.getCode());
                        } else {
                            planInfo.setIsProduct(IN_PRODUCTION.getCode());
                        }
                    }
                }
                List<InventoryForeignRedundantInfoDto> foreignRedundantInfo = foreignMap.get(inventoryInfoId);
                if (CollectionUtil.isNotEmpty(foreignRedundantInfo)) {
                    foreignInfo.addAll(foreignRedundantInfo);
                }

                List<InventoryOnShippingRedundantBO> onShippingRedundantInfo = onShippingRedundantMap.get(inventoryInfoId);
                if (CollectionUtil.isNotEmpty(onShippingRedundantInfo)) {
                    onShippingInfo.addAll(onShippingRedundantInfo);
                }
                List<InventoryFactoryRedundantInfoDto> factoryRedundantInfo = factoryMap.get(inventoryInfoId);
                if (CollectionUtil.isNotEmpty(factoryRedundantInfo)) {
                    factoryInfo.addAll(factoryRedundantInfo);
                }

                inventoryInfoByVirtual.put(inventoryInfoId, infoIdWithVirtual.get(inventoryInfoId));
            });
            dto.setVirtualSkuByInventoryInfoId(inventoryInfoByVirtual);
            dto.setChannelByInventoryInfoId(channel);
            dto.setForeignRedundantInfo(foreignInfo);
            dto.setOnShippingRedundantInfo(onShippingInfo);
            dto.setFactoryRedundantInfo(factoryInfo);
        });

        return InventoryFactoryAndForeignInfoDto.builder().factoryAndForeignInfoMap(resultMap).build();
    }

    @Override
    public InventoryFactoryAndForeignInfoDto getFactoryRedundantInfoInterior(List<String> selfSkuIdList) {
        InventoryInfoQuery form = new InventoryInfoQuery();
        form.setCurrent(1);
        form.setSize(-1);
        form.setSelfSkuIdList(selfSkuIdList);
        IPage<InventoryInfoDto> inventoryInfoDtoIPage = selectListByInfoIds(form);
        List<InventoryInfoDto> records = inventoryInfoDtoIPage.getRecords();
        Map<String, List<InventoryInfoDto>> inventoryInfoSelfIdMap = records.stream().collect(Collectors.groupingBy(InventoryInfoDto::getSelfSkuId));
        List<String> inventoryInfoIds = records.stream().map(InventoryInfoDto::getInventoryInfoId).toList();
        if (CollectionUtil.isEmpty(inventoryInfoIds)) {
            return InventoryFactoryAndForeignInfoDto.builder().factoryAndForeignInfoMap(new HashMap<>()).build();
        }
        var foreignRedundantList = inventoryForeignRedundantInfoService.getForeignRedundantList(inventoryInfoIds);
        List<InventoryForeignRedundantInfoDto> inventoryForeignRedundantInfoDto = redundantInventoryAssembler.foreignRedundantBOToDto(foreignRedundantList);
        List<InventoryFactoryRedundantInfoDto> factoryRedundantInfoList = inventoryFactoryRedundantInfoService.getFactoryRedundantInfoList(inventoryInfoIds);

        List<InventoryOnShippingRedundantBO> onShippingRedundantInfoList = inventoryWatchBoardService.getOnShippingRedundantInfoList(inventoryInfoIds)
                .stream()
                .filter(item -> item.getShipmentCode() != null)
                .toList();

        //List<InventoryForeignStoreDO> foreignStoreListByInfoIds = inventoryForeignStoreService.getForeignStoreListByInfoIds(inventoryInfoIds);

        //Map<String, List<InventoryForeignStoreDO>> foreignStoreMap = foreignStoreListByInfoIds.stream().collect(Collectors.groupingBy(InventoryForeignStoreDO::getInventoryInfoId));
        //List<InventoryOnShippingRedundantBO> onShippingRedundantListForExport = new ArrayList<>();
        //
        //onShippingRedundantInfoList.forEach(i -> {
        //    String inventoryInfoId = i.getInventoryInfoId();
        //    List<InventoryForeignStoreDO> foreignStoreDOList = foreignStoreMap.getOrDefault(inventoryInfoId, null);
        //    if (CollectionUtil.isNotEmpty(foreignStoreDOList)) {
        //        for (var y : foreignStoreDOList) {
        //            LocalDate enableUsingDate = LocalDate.from(LocalDateTimeUtil.of(y.getEnableUsingDate()));
        //            if (enableUsingDate.isEqual(i.getEnableUsingDate()) && i.getShipmentCode().equals(y.getShipmentCode())) {
        //                if (y.getStartShippingDate() != null) {
        //                    i.setStartShippingDate(LocalDate.from(LocalDateTimeUtil.of(y.getStartShippingDate())));
        //                    onShippingRedundantListForExport.add(i);
        //                } else {
        //                    onShippingRedundantListForExport.add(i);
        //                    break;
        //                }
        //            }
        //        }
        //        ;
        //    } else {
        //        onShippingRedundantListForExport.add(i);
        //    }
        //
        //});

        Set<String> collect = Stream.of(
                inventoryForeignRedundantInfoDto.stream().map(InventoryForeignRedundantInfoDto::getInventoryInfoId),
                onShippingRedundantInfoList.stream().map(InventoryOnShippingRedundantBO::getInventoryInfoId),
                factoryRedundantInfoList.stream().map(InventoryFactoryRedundantInfoDto::getInventoryInfoId)
        ).flatMap(Function.identity()).collect(Collectors.toSet());
        List<InventoryFactoryInfoA> redundantInventoryDOS = redundantInventoryRepository.selectVirtualByInfoIds(collect);
        Map<String, String> infoIdWithVirtual = redundantInventoryDOS.stream().collect(Collectors.toMap(InventoryFactoryInfoA::getInventoryInfoId, InventoryFactoryInfoA::getVirtualSku));
        Map<String, String> channel = redundantInventoryDOS.stream().collect(Collectors.toMap(InventoryFactoryInfoA::getInventoryInfoId, InventoryFactoryInfoA::getChannel));
        Map<String, Integer> borrowingStrategy = redundantInventoryDOS.stream().filter(i -> i.getBorrowingStrategy() != null).collect(Collectors.toMap(InventoryFactoryInfoA::getInventoryInfoId, InventoryFactoryInfoA::getBorrowingStrategy));

        // 先将 foreignRedundantList 按 inventoryInfoId 分组
        Map<String, List<InventoryForeignRedundantInfoDto>> foreignMap = inventoryForeignRedundantInfoDto.stream()
                .collect(Collectors.groupingBy(InventoryForeignRedundantInfoDto::getInventoryInfoId));

        // 冗余在途
        Map<String, List<InventoryOnShippingRedundantBO>> onShippingRedundantMap = onShippingRedundantInfoList.stream()
                .collect(Collectors.groupingBy(InventoryOnShippingRedundantBO::getInventoryInfoId));

        // 先将 factoryRedundantInfoList 按 inventoryInfoId 分组
        Map<String, List<InventoryFactoryRedundantInfoDto>> factoryMap = factoryRedundantInfoList.stream()
                .collect(Collectors.groupingBy(InventoryFactoryRedundantInfoDto::getInventoryInfoId));

        // 合并两个 map 到 resultMap 中
        Map<String, FactoryAndForeignInfoMapDto> resultMap = new HashMap<>();

        inventoryInfoSelfIdMap.forEach((selfSkuId, inventoryInfoList) -> {
            FactoryAndForeignInfoMapDto dto = resultMap.computeIfAbsent(selfSkuId, k -> new FactoryAndForeignInfoMapDto());
            List<InventoryForeignRedundantInfoDto> foreignInfo = new ArrayList<>();
            List<InventoryOnShippingRedundantBO> onShippingInfo = new ArrayList<>();
            List<InventoryFactoryRedundantInfoDto> factoryInfo = new ArrayList<>();
            Map<String, String> inventoryInfoByVirtual = new HashMap<>();
            inventoryInfoList.forEach(inventoryInfo -> {
                String inventoryInfoId = inventoryInfo.getInventoryInfoId();
                List<InventoryFactoryRedundantInfoDto> inventoryFactoryPlanInfoAS = factoryMap.get(inventoryInfoId);
                if (!CollectionUtil.isEmpty(inventoryFactoryPlanInfoAS)) {
                    for (InventoryFactoryRedundantInfoDto planInfo : inventoryFactoryPlanInfoAS) {
                        DateTime parse = DateUtil.parse(planInfo.getFactoryFinishedDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN);

                        if (DateUtil.compare(inventoryInfo.getCalFinishedDate(), parse) >= 0) {
                            planInfo.setIsProduct(PRODUCT.getCode());
                        } else {
                            planInfo.setIsProduct(IN_PRODUCTION.getCode());
                        }
                    }
                }
                List<InventoryForeignRedundantInfoDto> foreignRedundantInfo = foreignMap.get(inventoryInfoId);
                if (CollectionUtil.isNotEmpty(foreignRedundantInfo)) {
                    foreignInfo.addAll(foreignRedundantInfo);
                }

                List<InventoryOnShippingRedundantBO> onShippingRedundantInfo = onShippingRedundantMap.get(inventoryInfoId);
                if (CollectionUtil.isNotEmpty(onShippingRedundantInfo)) {
                    onShippingInfo.addAll(onShippingRedundantInfo);
                }
                List<InventoryFactoryRedundantInfoDto> factoryRedundantInfo = factoryMap.get(inventoryInfoId);
                if (CollectionUtil.isNotEmpty(factoryRedundantInfo)) {
                    factoryInfo.addAll(factoryRedundantInfo);
                }

                inventoryInfoByVirtual.put(inventoryInfoId, infoIdWithVirtual.get(inventoryInfoId));
            });
            dto.setVirtualSkuByInventoryInfoId(inventoryInfoByVirtual);
            dto.setChannelByInventoryInfoId(channel);
            dto.setBorrowingStrategyByInventoryInfoId(borrowingStrategy);
            dto.setForeignRedundantInfo(foreignInfo);
            dto.setOnShippingRedundantInfo(onShippingInfo);
            dto.setFactoryRedundantInfo(factoryInfo);
        });

        return InventoryFactoryAndForeignInfoDto.builder().factoryAndForeignInfoMap(resultMap).build();
    }

    @Override
    public boolean hasRedundancyByVirtualSkuId(List<String> virtualSkuIds) {
        RedundantInventoryDO redundantInventoryDO = redundantInventoryRepository.getRedundancyByVirtualSkuId(virtualSkuIds);
        return !ObjectUtil.isEmpty(redundantInventoryDO);
    }

    @Override
    public List<String> hasRedundancyByVirtualSkuIdList(List<String> virtualSkuIds) {
        List<RedundantInventoryDO> redundantInventoryDO = redundantInventoryRepository.getRedundancyByVirtualSkuIdList(virtualSkuIds);
        if (CollectionUtil.isNotEmpty(redundantInventoryDO)){
            return redundantInventoryDO.stream().map(RedundantInventoryDO::getVirtualSkuId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 删除库存信息
     *
     * @param infoIds 需要删除的库存ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFactoryByInfoId(List<String> infoIds) {
        if (CollectionUtil.isEmpty(infoIds)) {
            return true;
        }

        for (String infoId : infoIds) {
            RedundantInventoryDO redundantInventoryDO = redundantInventoryRepository.getById(infoId);
            if (ObjectUtil.isEmpty(redundantInventoryDO)) {
                continue;
            }
            // 删除模拟试算表格
            inventoryMockTableService.deleteInventoryMockTableByInfoId(infoId);
            // 删除试算看板表格
            List<InventoryForeignShipWatchBoardDO> inventoryWatchBoardByInfoId = inventoryWatchBoardService.getForeignAndShipInventoryWatchBoardByInfoId(infoId);
            List<String> watchBoarIds = inventoryWatchBoardByInfoId.stream().map(InventoryForeignShipWatchBoardDO::getId).toList();
            inventoryForeignRedundantInfoService.deleteByWatchBoardIds(watchBoarIds);
            inventoryFactoryRedundantInfoService.deleteByWatchBoardIds(watchBoarIds);
            inventoryWatchBoardService.deleteByInventoryId(infoId);
            // 删除海外仓在途、库存信息
            inventoryForeignStoreService.deleteForeignStoreInfoByInfoId(infoId);
            // 删除试算规则
            inventorySaleRulesService.deleteInventorySaleByInfoId(infoId);
            // 删除工厂交货信息
            inventoryFactoryPlanInfoService.deleteByInventoryInfoId(infoId);
            // 删除售罄时间
            soldOutDaysService.deleteByInventoryInfoId(infoId);
            redundantInventoryRepository.removeById(infoId);

            // 删除无计划库存预警信息
            unsalableInventorySaveService.deleteUnsalableInventoryByRedundancyInventoryId(infoId);
            // 删除加急补货信息
            urgentPurchaseSaveService.deleteUrgentPurchaseByInventoryId(infoId);
        }
        return true;
    }

    @Override
    public List<OperatorSearchVo> getOperator() {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        UserParams params = new UserParams();
        params.setPostId(UserPostEnum.OPERATIONS_MANAGER.getCode());
        params.setUserStatus(UserStatusEnum.YES_STATUS.getCode());
        ResultDTO resultDTO = restTemplateUtil.post(params, ResultDTO.class, SYS_USER_INFO);
        List userList = JSON.to(List.class, resultDTO.getData());
        List<OperatorSearchVo> operatorSearchVo = new ArrayList<>();
        userList.forEach(i -> {
            UserInteriorVO userInteriorVO = JSON.to(UserInteriorVO.class, i);
            operatorSearchVo.add(OperatorSearchVo.builder().userName(userInteriorVO.getUserName()).nickName(userInteriorVO.getNickName()).build());
        });
        return operatorSearchVo;
    }

    @Override
    public Set<String> getSnapIds(InventoryInfoQuery infoQuery) {

        return redundantInventoryRepository.getSnapIds(infoQuery);
    }

    @Override
    public InventorySaleRulesDO getSaleDestination(String inventoryInfoId) {

        return inventorySaleRulesService.getInventorySaleByInfoId(inventoryInfoId);
    }

    @Override
    public boolean updateSaleDestination(InventorySaleRulesDO saleRulesDO) {

        return inventorySaleRulesService.updateInventorySaleRules(saleRulesDO);
    }
}
